# Avatar Upload Functionality

This document describes the avatar upload functionality that has been added to the Sohnus platform.

## Overview

The avatar upload feature allows workers to upload a profile photo when submitting their job application form. The image is uploaded to Google Cloud Storage and the URL is stored in the NocoDB database.

## Architecture

### Frontend (React)
- **File**: `src/components/WorkerFormSection.tsx`
- **Features**:
  - File input with image preview
  - File validation (type and size)
  - Base64 encoding for upload
  - Error handling with user feedback

### Backend (Netlify Functions)
- **Main Function**: `netlify/functions/upload-avatar.cjs`
- **Test Function**: `netlify/functions/test-avatar-upload.cjs`
- **Features**:
  - Google Cloud Storage integration
  - NocoDB record updates
  - File validation and processing
  - Error handling and logging

## Setup Requirements

### Environment Variables

Add the following environment variables to your Netlify deployment:

```bash
# Google Cloud Storage Configuration
GCS_PROJECT_ID=your-gcs-project-id
GCS_BUCKET_NAME=your-gcs-bucket-name
GCS_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GCS_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...your-private-key...\n-----END PRIVATE KEY-----"

# NocoDB Configuration (already existing)
NOCODB_URL=your-nocodb-url
NOCODB_TOKEN=your-nocodb-token
```

### Google Cloud Storage Setup

1. **Create a GCS Bucket**:
   ```bash
   gsutil mb gs://your-bucket-name
   ```

2. **Set Bucket Permissions**:
   ```bash
   gsutil iam ch allUsers:objectViewer gs://your-bucket-name
   ```

3. **Create Service Account**:
   - Go to Google Cloud Console → IAM & Admin → Service Accounts
   - Create a new service account
   - Grant "Storage Object Admin" role
   - Generate and download JSON key
   - Extract `client_email` and `private_key` for environment variables

### NocoDB Configuration

Ensure your worker table (`mcv0hz2nurqap37`) has an `Avatar` field of type "URL" or "Text".

## Usage

### Frontend Integration

The avatar upload is integrated into the worker form:

```tsx
// Avatar upload section in the form
<div className="space-y-2">
  <Label htmlFor="avatar">{t('worker_form.fields.avatar')} <span className="text-sm text-muted-foreground">({t('common.optional')})</span></Label>
  <div className="flex items-center space-x-4">
    {avatarPreview ? (
      <div className="relative">
        <img src={avatarPreview} alt="Avatar preview" className="w-20 h-20 rounded-full object-cover border-2 border-gray-200" />
        <button type="button" onClick={removeAvatar}>×</button>
      </div>
    ) : (
      <div className="w-20 h-20 rounded-full bg-gray-100 border-2 border-dashed border-gray-300">
        <span className="text-gray-400 text-xs">No Image</span>
      </div>
    )}
    <input id="avatar" type="file" accept="image/*" onChange={handleAvatarChange} />
  </div>
</div>
```

### API Endpoints

#### Upload Avatar
- **Endpoint**: `/api/upload-avatar`
- **Method**: POST
- **Content-Type**: application/json
- **Body**:
  ```json
  {
    "recordId": "string",
    "fileName": "string", 
    "fileData": "data:image/jpeg;base64,..."
  }
  ```

#### Test Avatar Upload
- **Endpoint**: `/api/test-avatar-upload`
- **Method**: POST
- **Purpose**: Test environment configuration and upload functionality

## File Validation

### Frontend Validation
- **File Type**: Only image files (`image/*`)
- **File Size**: Maximum 5MB
- **Preview**: Real-time image preview

### Backend Validation
- **MIME Type**: Validates `image/*` from data URL
- **File Size**: Validates estimated size from base64 data
- **Required Fields**: Ensures `recordId`, `fileName`, and `fileData` are present

## Error Handling

### Frontend Errors
- Invalid file type → Toast notification
- File too large → Toast notification
- Upload failure → Warning toast (doesn't fail form submission)

### Backend Errors
- Missing environment variables → 500 error with details
- Invalid file data → 400 error with validation message
- GCS upload failure → 500 error with details
- NocoDB update failure → 500 error with details

## Workflow

1. **User selects image** → Frontend validates and shows preview
2. **Form submission** → Form data submitted to `/api/submit-form`
3. **Form success** → If avatar present, upload to `/api/upload-avatar`
4. **Avatar upload** → File uploaded to GCS, URL stored in NocoDB
5. **Completion** → User sees success message

## Testing

### Manual Testing
1. Visit the worker form
2. Fill required fields
3. Upload an image file
4. Submit the form
5. Check NocoDB for the avatar URL
6. Verify image is accessible via the GCS URL

### Automated Testing
Run the test function:
```bash
curl -X POST https://your-site.netlify.app/api/test-avatar-upload
```

## Troubleshooting

### Common Issues

1. **Environment Variables Missing**
   - Check Netlify environment variables
   - Ensure GCS_PRIVATE_KEY has proper newlines

2. **GCS Upload Fails**
   - Verify service account permissions
   - Check bucket exists and is accessible
   - Validate GCS project ID

3. **NocoDB Update Fails**
   - Ensure Avatar field exists in worker table
   - Check NocoDB token permissions
   - Verify table ID is correct

4. **File Too Large**
   - Frontend: Check file size before upload
   - Backend: Validate base64 data size

### Debugging

Enable detailed logging by checking Netlify function logs:
- Go to Netlify Dashboard → Functions → View logs
- Look for console.log outputs from upload-avatar function

## Security Considerations

- Files are validated for type and size
- GCS bucket has public read access for uploaded files
- Service account has minimal required permissions
- No direct file system access on serverless functions
- Base64 encoding prevents binary data issues

## Future Enhancements

- Image resizing/optimization before upload
- Multiple image formats support
- Image compression
- CDN integration for faster delivery
- Batch upload support
- Image editing capabilities
