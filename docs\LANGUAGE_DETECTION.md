# Automatic Language Detection

The Sohnus job platform now includes automatic language detection that provides a better user experience by showing content in the user's preferred language immediately upon their first visit.

## How It Works

### 1. Detection Logic
The system uses the browser's `navigator.language` and `navigator.languages` API to detect the user's preferred language settings.

### 2. Language Mapping
Browser language codes are mapped to our supported languages:
- `en` or `en-*` (e.g., `en-US`, `en-GB`) → English (en)
- `de` or `de-*` (e.g., `de-DE`, `de-AT`) → German (de)
- `zh` or `zh-*` (e.g., `zh-CN`, `zh-TW`) → Chinese (zh)
- `es` or `es-*` (e.g., `es-ES`, `es-MX`) → Spanish (es)

### 3. Fallback Behavior
If the detected language is not supported, the system defaults to English (en).

### 4. User Preference Respect
- **First Visit**: Auto-detects browser language and saves preference
- **Return Visits**: Uses previously saved language preference
- **Manual Override**: User can always change language via the LanguageSwitcher component

## Implementation Details

### Location
The detection logic is implemented in `src/contexts/I18nContext.tsx` within the `getInitialLanguage()` function.

### Process Flow
1. Check if user has a saved language preference in localStorage
2. If yes, use the saved preference
3. If no (first visit), detect browser language
4. Map detected language to supported language
5. Save detected language to localStorage
6. Load appropriate translations

### Code Example
```typescript
const detectBrowserLanguage = (): Language => {
  const browserLanguages = navigator.languages || [navigator.language];
  const supportedLanguages: Language[] = ['en', 'de', 'zh', 'es'];
  
  for (const browserLang of browserLanguages) {
    const langCode = browserLang.toLowerCase();
    
    // Direct match
    if (supportedLanguages.includes(langCode as Language)) {
      return langCode as Language;
    }
    
    // Language family match
    const langFamily = langCode.split('-')[0];
    if (supportedLanguages.includes(langFamily as Language)) {
      return langFamily as Language;
    }
  }
  
  return 'en'; // Fallback to English
};
```

## Testing

### Manual Testing
1. Clear browser localStorage: `localStorage.removeItem('sohnus-language')`
2. Change browser language settings
3. Refresh the page
4. Verify the correct language is detected and displayed

### Browser Language Settings
- **Chrome**: Settings → Advanced → Languages
- **Firefox**: Settings → General → Language
- **Safari**: System Preferences → Language & Region
- **Edge**: Settings → Languages

### Test Cases
- English browser → Should show English content
- German browser → Should show German content
- Chinese browser → Should show Chinese content
- Spanish browser → Should show Spanish content
- Unsupported language → Should default to English

## Compatibility

### Browser Support
- Modern browsers that support `navigator.languages` (Chrome 32+, Firefox 32+, Safari 10.1+)
- Fallback to `navigator.language` for older browsers

### No Disruption
- Existing LanguageSwitcher component continues to work normally
- Manual language selection always overrides auto-detection
- No impact on loading performance or user experience

## Debugging

### Console Logs
The system provides detailed console logs for debugging:
- `🌐 Browser languages detected: [...]`
- `🎯 Direct language match found: ...`
- `🎯 Language family match found: ...`
- `🔧 Using saved language preference: ...`
- `🔧 First visit detected, auto-detecting language...`
- `💾 Saved auto-detected language: ...`

### Demo Component
A `LanguageDetectionDemo` component is available for testing purposes that can be temporarily added to any page to inspect the detection behavior.

## Benefits

1. **Better UX**: Users see content in their preferred language immediately
2. **Increased Engagement**: Reduces language barrier on first visit
3. **Global Accessibility**: Supports international users better
4. **Seamless Integration**: Works alongside existing language switching functionality
5. **Respectful**: Always honors user's manual language choices
