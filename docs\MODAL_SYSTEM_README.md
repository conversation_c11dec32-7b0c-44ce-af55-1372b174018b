# Modal Dialog System Documentation

## Overview

This modal dialog system provides a welcome screen for first-time visitors to choose between "Looking for a Job" and "Hiring" paths, with different color schemes for each user type.

## Features

### Modal Component (`WelcomeModal.tsx`)
- **Automatic Display**: Shows on first visit to the website
- **Accessibility**: Full keyboard navigation, ARIA labels, focus trap
- **Responsive Design**: Works on all screen sizes
- **Backdrop Overlay**: Focuses attention on the modal
- **Two Action Buttons**:
  - "Looking for a Job": Closes modal, stays on current page
  - "Hiring": Redirects to `/hiring` page with employer color scheme

### Page Structure
- **Main Page** (`/`): Default job seeker theme with bright yellow/blue colors
- **Hiring Page** (`/hiring`): Employer theme with professional blue/teal colors
- **Identical Content**: Both pages use the same components and layout

### Color Schemes
- **Job Seeker Theme**: Bright yellow primary, blue secondary
- **Employer Theme**: Professional blue/teal primary, navy secondary

## Technical Implementation

### Files Created/Modified
1. `src/components/WelcomeModal.tsx` - Main modal component
2. `src/pages/Hiring.tsx` - Hiring page component
3. `src/utils/modalHelpers.ts` - Utility functions for modal management
4. `public/modal-image.svg` - Placeholder image for modal
5. `src/index.css` - Added hiring theme CSS variables
6. `src/App.tsx` - Added routing for hiring page
7. `src/pages/Index.tsx` - Integrated modal functionality

### Key Features
- **Local Storage**: Tracks first visit using `sohnus-has-visited` key
- **React Router**: Handles navigation between pages
- **CSS Variables**: Easy theme switching via `.hiring-theme` class
- **TypeScript**: Full type safety throughout

## Testing the Modal

### For Development
1. **Reset Modal Flag**: Open browser console and run:
   ```javascript
   window.resetModalFlag()
   ```
   Then refresh the page to see the modal again.

2. **Clear Local Storage**: Manually clear the `sohnus-has-visited` key from localStorage

### Testing Scenarios
1. **First Visit**: Clear localStorage and visit `/` - modal should appear
2. **Job Seeker Path**: Click "Looking for a Job" - modal closes, stays on main page
3. **Hiring Path**: Click "Hiring" - redirects to `/hiring` with blue theme
4. **Return Visit**: Visit `/` again - modal should not appear
5. **Direct Hiring Access**: Visit `/hiring` directly - no modal, blue theme active

## Accessibility Features

- **Keyboard Navigation**: Tab through buttons, Escape to close
- **Focus Trap**: Focus stays within modal when open
- **ARIA Labels**: Proper screen reader support
- **Semantic HTML**: Proper dialog role and labeling
- **Color Contrast**: High contrast ratios for both themes

## Customization

### Adding New Themes
1. Add new CSS variables in `src/index.css`
2. Create a new theme class (e.g., `.custom-theme`)
3. Apply the class to the page wrapper

### Modifying Modal Content
- Edit `src/components/WelcomeModal.tsx`
- Replace `public/modal-image.svg` with custom image
- Update button text and actions as needed

### Changing Color Schemes
- Modify CSS variables in `src/index.css`
- Update both light and hiring theme sections
- Use HSL color format for consistency

## Browser Support

- Modern browsers with ES6+ support
- Local Storage support required
- CSS custom properties support required

## Performance Considerations

- Modal only loads on first visit
- Lightweight SVG placeholder image
- CSS-only animations for smooth performance
- Minimal JavaScript for modal logic
