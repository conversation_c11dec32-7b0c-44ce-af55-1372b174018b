# Design Token System Documentation

## Overview

The Sohnus job platform uses a comprehensive design token system that provides distinct visual experiences for job seekers and employers while maintaining code reusability and design consistency.

## Architecture

### Theme Structure
- **Job Seeker Theme** (`job-seeker-theme`): Warm, energetic, approachable (yellow/orange palette)
- **Employer Theme** (`employer-theme`): Professional, trustworthy, authoritative (blue/teal palette)
- **Base Tokens**: Shared design foundations (typography, spacing, transitions)

### Token Categories

#### 1. Color Tokens
```css
/* Job Seeker Theme (Default) */
--primary: 56 100% 58%;           /* Bright yellow */
--secondary: 244.25, 100%, 22.16%; /* Deep blue */
--accent: 217 91% 60%;            /* Blue accent */

/* Employer Theme */
--primary: 195 100% 45%;          /* Professional teal */
--secondary: 220 100% 25%;        /* Deep navy */
--accent: 25 95% 55%;             /* Complementary orange */
```

#### 2. Typography Tokens
```css
--font-size-xs: 0.75rem;
--font-size-sm: 0.875rem;
--font-size-base: 1rem;
--font-size-lg: 1.125rem;
--font-size-xl: 1.25rem;
--font-size-2xl: 1.5rem;
--font-size-3xl: 1.875rem;
--font-size-4xl: 2.25rem;
```

#### 3. Spacing Tokens
```css
--spacing-xs: 0.25rem;
--spacing-sm: 0.5rem;
--spacing-md: 1rem;
--spacing-lg: 1.5rem;
--spacing-xl: 2rem;
--spacing-2xl: 3rem;
--spacing-3xl: 4rem;
```

#### 4. Transition Tokens
```css
--transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
--transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
--transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
```

## Usage

### 1. Theme Context
```typescript
import { useTheme } from '@/contexts/ThemeContext';

const MyComponent = () => {
  const { currentTheme, isJobSeekerTheme, isEmployerTheme } = useTheme();
  // Theme automatically switches based on route
};
```

### 2. Design Token Hook
```typescript
import { useDesignTokens } from '@/hooks/useDesignTokens';

const MyComponent = () => {
  const { tokens, themeClasses, combineClasses } = useDesignTokens();
  
  return (
    <button className={combineClasses(tokens.button.primary, themeClasses.ctaButton)}>
      Submit
    </button>
  );
};
```

### 3. CSS Utility Classes
```css
/* Theme-specific utilities */
.job-seeker-cta-emphasis:hover {
  transform: scale(1.02);
}

.employer-cta-emphasis:hover {
  transform: scale(1.01);
}

/* Token-based spacing */
.p-token-lg { padding: var(--spacing-lg); }
.gap-token-md { gap: var(--spacing-md); }
```

## Component Token Patterns

### Button Variants
```typescript
const buttonTokens = {
  primary: "bg-primary text-primary-foreground hover:bg-primary/90",
  secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/90",
  accent: "bg-accent text-accent-foreground hover:bg-accent/90",
  outline: "border border-border bg-background hover:bg-accent",
  ghost: "text-foreground hover:bg-accent"
};
```

### Card Variants
```typescript
const cardTokens = {
  default: "bg-card text-card-foreground rounded-lg border shadow-card",
  elevated: "bg-card text-card-foreground rounded-lg border shadow-soft hover:shadow-card",
  interactive: "bg-card text-card-foreground rounded-lg border shadow-card hover:shadow-soft cursor-pointer"
};
```

### Form Elements
```typescript
const formTokens = {
  input: "bg-form-background border border-form-border focus:border-form-focus",
  label: "text-sm font-medium text-foreground",
  error: "text-sm text-form-error",
  success: "text-sm text-form-success"
};
```

## Theme Switching

### Automatic Theme Detection
- **Route-based**: `/` = job-seeker theme, `/hiring` = employer theme
- **Body class application**: Automatically applies theme classes to document body
- **CSS variable override**: Theme-specific tokens override base tokens

### Manual Theme Override
```typescript
// For special cases where manual theme control is needed
const { getThemeClass } = useDesignTokens();

const className = getThemeClass('base-class', {
  jobSeeker: 'job-seeker-variant',
  employer: 'employer-variant'
});
```

## Best Practices

### 1. Use Design Tokens
✅ **Do**: Use design tokens for consistent styling
```typescript
className={tokens.button.primary}
```

❌ **Don't**: Use hardcoded Tailwind classes for theme-dependent styling
```typescript
className="bg-yellow-500 hover:bg-yellow-600"
```

### 2. Combine Classes Properly
✅ **Do**: Use the combineClasses utility
```typescript
className={combineClasses(tokens.card.default, themeClasses.formSection, "custom-class")}
```

❌ **Don't**: Manually concatenate class strings
```typescript
className={`${tokens.card.default} ${themeClasses.formSection} custom-class`}
```

### 3. Theme-Aware Components
✅ **Do**: Create components that adapt to theme context
```typescript
const Button = ({ variant = 'primary', ...props }) => {
  const { tokens, themeClasses } = useDesignTokens();
  return (
    <button 
      className={combineClasses(tokens.button[variant], themeClasses.ctaButton)}
      {...props}
    />
  );
};
```

### 4. Consistent Spacing
✅ **Do**: Use spacing tokens for consistent layout
```css
.section { padding: var(--spacing-2xl) var(--spacing-lg); }
```

❌ **Don't**: Use arbitrary spacing values
```css
.section { padding: 48px 24px; }
```

## Extending the System

### Adding New Tokens
1. Add base token to `:root` in `src/index.css`
2. Add theme-specific overrides in theme classes
3. Update `useDesignTokens` hook if needed
4. Document the new token usage

### Adding New Themes
1. Create new theme class in CSS
2. Update `ThemeContext` to support new theme
3. Add theme detection logic
4. Update design token hook

## File Structure
```
src/
├── contexts/
│   └── ThemeContext.tsx          # Theme management
├── hooks/
│   └── useDesignTokens.ts        # Design token utilities
├── index.css                     # Token definitions
└── components/
    └── [components using tokens]
```

This design token system ensures consistent, maintainable, and theme-aware styling across the entire application while providing distinct experiences for different user types.
