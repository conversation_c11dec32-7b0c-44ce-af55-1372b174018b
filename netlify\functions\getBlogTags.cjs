const { Client } = require('@notionhq/client');

// CORS headers for browser compatibility
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  console.log('getBlogTags function called');

  // Extract audience and language parameters from query string
  const audience = event.queryStringParameters?.audience || null;
  const language = event.queryStringParameters?.language || null;
  console.log('Audience filter requested for tags:', audience);
  console.log('Language filter requested for tags:', language);

  // <PERSON>le preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Validate environment variables
    const notionToken = process.env.NOTION_TOKEN;
    const notionDatabaseId = process.env.NOTION_DATABASE_ID;

    if (!notionToken) {
      console.error('NOTION_TOKEN environment variable is not set');
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          error: 'Server configuration error: Missing Notion token',
          tags: []
        })
      };
    }

    if (!notionDatabaseId) {
      console.error('NOTION_DATABASE_ID environment variable is not set');
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          error: 'Server configuration error: Missing database ID',
          tags: []
        })
      };
    }

    // Initialize Notion client
    const notion = new Client({
      auth: notionToken
    });

    console.log('Fetching published blog posts to extract tags...');

    // Build query filters
    const filters = [
      {
        property: 'Published',
        checkbox: {
          equals: true
        }
      }
    ];

    // Add audience filter if specified
    if (audience) {
      filters.push({
        or: [
          {
            property: 'Audience',
            select: {
              equals: audience
            }
          },
          {
            property: 'Audience',
            select: {
              equals: 'Both'
            }
          }
        ]
      });
      console.log(`Added audience filter for tags: ${audience} OR Both`);
    }

    // Add language filter if specified
    if (language) {
      filters.push({
        property: 'Language',
        select: {
          equals: language
        }
      });
      console.log(`Added language filter for tags: ${language}`);
    }

    // Query all published blog posts to extract unique tags
    const response = await notion.databases.query({
      database_id: notionDatabaseId,
      filter: filters.length === 1 ? filters[0] : { and: filters },
      page_size: 100 // Get all posts to extract all tags
    });

    console.log(`Found ${response.results.length} published blog posts`);

    // Extract all tags from all posts
    const allTags = new Set();
    
    response.results.forEach(page => {
      const tagsProperty = page.properties.Tags;
      
      if (tagsProperty && tagsProperty.multi_select) {
        tagsProperty.multi_select.forEach(tag => {
          if (tag.name && tag.name.trim()) {
            allTags.add(tag.name.trim());
          }
        });
      }
    });

    // Convert Set to sorted array
    const uniqueTags = Array.from(allTags).sort();

    console.log(`Extracted ${uniqueTags.length} unique tags:`, uniqueTags);

    return {
      statusCode: 200,
      headers: {
        ...corsHeaders,
        'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
      },
      body: JSON.stringify({
        success: true,
        tags: uniqueTags,
        total: uniqueTags.length
      })
    };

  } catch (error) {
    console.error('Error in getBlogTags function:', error);
    
    // Handle specific Notion API errors
    if (error.code === 'unauthorized') {
      return {
        statusCode: 401,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          error: 'Authentication failed: Invalid Notion token',
          tags: []
        })
      };
    }

    if (error.code === 'object_not_found') {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          error: 'Database not found: Invalid database ID',
          tags: []
        })
      };
    }

    // Generic error response
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: 'Failed to fetch blog tags',
        tags: []
      })
    };
  }
};
