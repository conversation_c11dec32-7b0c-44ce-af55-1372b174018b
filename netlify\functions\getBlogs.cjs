/**
 * Netlify Function: getBlogs
 *
 * Fetches blog posts from Notion database for the Sohnus job platform
 * Returns blog posts filtered by language with proper error handling
 */

const { Client } = require('@notionhq/client');

exports.handler = async (event, context) => {
  // Set CORS headers for browser compatibility
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json',
    'Cache-Control': 'public, max-age=300' // 5 minutes cache
  };

  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  // Extract audience parameter from query string
  const audience = event.queryStringParameters?.audience || null;
  console.log('Audience filter requested:', audience);

  // Only allow GET requests
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Method not allowed. Only GET requests are supported.',
        posts: [],
        total: 0
      })
    };
  }

  // Validate environment variables
  const notionToken = process.env.NOTION_TOKEN;
  const notionDatabaseId = process.env.NOTION_DATABASE_ID;

  if (!notionToken) {
    console.error('Missing NOTION_TOKEN environment variable');
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Server configuration error: Missing Notion integration token.',
        posts: [],
        total: 0
      })
    };
  }

  if (!notionDatabaseId) {
    console.error('Missing NOTION_DATABASE_ID environment variable');
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Server configuration error: Missing Notion database ID.',
        posts: [],
        total: 0
      })
    };
  }

  // Validate token format
  if (!notionToken.startsWith('secret_') && !notionToken.startsWith('ntn_')) {
    console.error('Invalid NOTION_TOKEN format');
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Server configuration error: Invalid Notion token format.',
        posts: [],
        total: 0
      })
    };
  }

  try {
    // Initialize Notion client
    const notion = new Client({
      auth: notionToken,
    });

    // Get query parameters
    const queryParams = event.queryStringParameters || {};
    const requestedLanguage = queryParams.language;
    const limit = parseInt(queryParams.limit) || 10;
    const offset = parseInt(queryParams.offset) || 0;

    // Build Notion database query - start with basic structure
    const notionQuery = {
      database_id: notionDatabaseId,
      page_size: Math.min(limit + offset, 100) // Notion API limit is 100
    };

    // Try to add sorting (this might fail if Date property doesn't exist)
    try {
      notionQuery.sorts = [
        {
          property: 'Date',
          direction: 'descending'
        }
      ];
    } catch (error) {
      console.log('Could not add Date sorting, will sort client-side');
    }

    // Add filters if needed
    const filters = [];

    // Add published filter using the 'Published' property (likely a checkbox)
    try {
      filters.push({
        property: 'Published',
        checkbox: {
          equals: true
        }
      });
    } catch (error) {
      console.log('Published property not found, skipping published filter');
    }

    // Add language filter if specified
    if (requestedLanguage) {
      try {
        filters.push({
          property: 'Language',
          select: {
            equals: requestedLanguage
          }
        });
      } catch (error) {
        console.log('Language property not found, will filter client-side');
      }
    }

    // Add audience filter if specified
    if (audience) {
      try {
        // Filter for posts that match the audience OR are marked as "Both"
        filters.push({
          or: [
            {
              property: 'Audience',
              select: {
                equals: audience
              }
            },
            {
              property: 'Audience',
              select: {
                equals: 'Both'
              }
            }
          ]
        });
        console.log(`Added audience filter for: ${audience} OR Both`);
      } catch (error) {
        console.log('Audience property not found, will filter client-side');
      }
    }

    // Apply filters if any exist
    if (filters.length > 0) {
      if (filters.length === 1) {
        notionQuery.filter = filters[0];
      } else {
        notionQuery.filter = {
          and: filters
        };
      }
    }

    console.log('Querying Notion database with:', JSON.stringify(notionQuery, null, 2));

    // Query Notion database
    const response = await notion.databases.query(notionQuery);

    console.log(`Found ${response.results.length} pages from Notion`);

    // Transform Notion pages to blog post format
    const transformedPosts = response.results.map(page => {
      try {
        // Extract properties with optional chaining
        const properties = page.properties || {};

        // Get title using exact property name
        const titleProperty = properties.Title;
        const title = titleProperty?.title?.[0]?.plain_text || 'Untitled';

        // Get excerpt using exact property name
        const excerptProperty = properties.Excerpt;
        const excerpt = excerptProperty?.rich_text?.[0]?.plain_text || '';

        // Get cover image using exact property name
        const coverProperty = properties.Cover;
        let coverImage = '';
        if (coverProperty?.files?.[0]) {
          const file = coverProperty.files[0];
          coverImage = file.file?.url || file.external?.url || '';
        }

        // Get published date using exact property name
        const dateProperty = properties.Date;
        const publishedDate = dateProperty?.date?.start || new Date().toISOString();

        // Get slug using exact property name
        const slugProperty = properties.Slug;
        const slug = slugProperty?.rich_text?.[0]?.plain_text || title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

        // Get language using exact property name
        const languageProperty = properties.Language;
        const language = languageProperty?.select?.name || 'en';

        // Get tags using exact property name
        const tagsProperty = properties.Tags;
        const tags = tagsProperty?.multi_select?.map(tag => tag.name) || [];

        // Get audience using exact property name
        const audienceProperty = properties.Audience;
        const audienceValue = audienceProperty?.select?.name || 'Both';

        return {
          id: page.id,
          title,
          excerpt,
          coverImage,
          publishedDate,
          slug,
          language,
          tags,
          audience: audienceValue
        };
      } catch (error) {
        console.error('Error transforming page:', page.id, error);
        return null;
      }
    }).filter(post => post !== null); // Remove any failed transformations

    console.log(`Successfully transformed ${transformedPosts.length} posts`);

    // Apply client-side filtering and sorting
    let filteredPosts = transformedPosts;

    // Filter by language if specified and not already filtered by Notion
    if (requestedLanguage) {
      filteredPosts = filteredPosts.filter(post => post.language === requestedLanguage);
    }

    // Filter by audience if specified and not already filtered by Notion
    if (audience) {
      filteredPosts = filteredPosts.filter(post =>
        post.audience === audience || post.audience === 'Both'
      );
      console.log(`After audience filtering (${audience}): ${filteredPosts.length} posts`);
    }

    // Sort by published date (newest first) if not already sorted by Notion
    filteredPosts.sort((a, b) => new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime());

    console.log(`After filtering: ${filteredPosts.length} posts`);

    // Apply client-side pagination
    const paginatedPosts = filteredPosts.slice(offset, offset + limit);

    // Return successful response
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        posts: paginatedPosts,
        total: filteredPosts.length,
        pagination: {
          limit,
          offset,
          hasMore: offset + limit < filteredPosts.length
        }
      })
    };

  } catch (error) {
    console.error('Error in getBlogs function:', error);

    // Handle specific Notion API errors
    let errorMessage = 'Internal server error occurred while fetching blog posts.';
    let statusCode = 500;

    if (error.code === 'unauthorized') {
      errorMessage = 'Authentication failed: Invalid Notion token.';
      statusCode = 401;
    } else if (error.code === 'object_not_found') {
      errorMessage = 'Notion database not found or not accessible.';
      statusCode = 404;
    } else if (error.code === 'rate_limited') {
      errorMessage = 'Rate limit exceeded. Please try again later.';
      statusCode = 429;
    } else if (error.code === 'invalid_request') {
      errorMessage = 'Invalid request to Notion API.';
      statusCode = 400;
    } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
      errorMessage = 'Network error: Unable to connect to Notion API.';
      statusCode = 503;
    }

    // Return error response with guaranteed JSON structure
    return {
      statusCode,
      headers,
      body: JSON.stringify({
        success: false,
        error: errorMessage,
        posts: [],
        total: 0,
        pagination: {
          limit: parseInt(event.queryStringParameters?.limit) || 10,
          offset: parseInt(event.queryStringParameters?.offset) || 0,
          hasMore: false
        },
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    };
  }
};
