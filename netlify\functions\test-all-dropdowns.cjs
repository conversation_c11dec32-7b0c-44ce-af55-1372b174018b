const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

exports.handler = async (event) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const nocoDBUrl = process.env.NOCODB_URL;
    const nocoDBToken = process.env.NOCODB_TOKEN;

    if (!nocoDBUrl || !nocoDBToken) {
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Missing NocoDB environment variables'
        })
      };
    }

    const baseUrl = nocoDBUrl.endsWith('/') ? nocoDBUrl.slice(0, -1) : nocoDBUrl;
    const workerTableId = 'mcv0hz2nurqap37';
    const companyTableId = 'm2yux6be57tujg8';

    // Test different dropdown values to see which ones are accepted
    const workerTestCases = [
      {
        name: "Education Level - No degree",
        data: {
          FirstName: "Test",
          LastName: "User",
          Email: "<EMAIL>",
          PhoneNumber: "+49 **********",
          City: "Berlin",
          DesiredPosition: "Test Position",
          EmploymentType: "full_time",
          Job1_Title: "Previous Job",
          Job1_Company: "Previous Company",
          Job1_Duration: "1 year",
          Job1_Tasks: "Test tasks",
          EducationLevel: "no_degree",
          GermanLevel: "b2",
          OtherLanguages: "English",
          DrivingLicense: "class_b",
          SkillsAndCerts: "Test skills",
          formType: "worker"
        }
      },
      {
        name: "Education Level - Vocational training",
        data: {
          FirstName: "Test",
          LastName: "User",
          Email: "<EMAIL>",
          PhoneNumber: "+49 **********",
          City: "Berlin",
          DesiredPosition: "Test Position",
          EmploymentType: "full_time",
          Job1_Title: "Previous Job",
          Job1_Company: "Previous Company",
          Job1_Duration: "1 year",
          Job1_Tasks: "Test tasks",
          EducationLevel: "vocational_training",
          GermanLevel: "b2",
          OtherLanguages: "English",
          DrivingLicense: "class_b",
          SkillsAndCerts: "Test skills",
          formType: "worker"
        }
      },
      {
        name: "Education Level - High school",
        data: {
          FirstName: "Test",
          LastName: "User",
          Email: "<EMAIL>",
          PhoneNumber: "+49 **********",
          City: "Berlin",
          DesiredPosition: "Test Position",
          EmploymentType: "full_time",
          Job1_Title: "Previous Job",
          Job1_Company: "Previous Company",
          Job1_Duration: "1 year",
          Job1_Tasks: "Test tasks",
          EducationLevel: "high_school",
          GermanLevel: "b2",
          OtherLanguages: "English",
          DrivingLicense: "class_b",
          SkillsAndCerts: "Test skills",
          formType: "worker"
        }
      },
      {
        name: "Education Level - Bachelor's degree",
        data: {
          FirstName: "Test",
          LastName: "User",
          Email: "<EMAIL>",
          PhoneNumber: "+49 **********",
          City: "Berlin",
          DesiredPosition: "Test Position",
          EmploymentType: "full_time",
          Job1_Title: "Previous Job",
          Job1_Company: "Previous Company",
          Job1_Duration: "1 year",
          Job1_Tasks: "Test tasks",
          EducationLevel: "bachelors_degree",
          GermanLevel: "b2",
          OtherLanguages: "English",
          DrivingLicense: "class_b",
          SkillsAndCerts: "Test skills",
          formType: "worker"
        }
      },
      {
        name: "German Level - Native",
        data: {
          FirstName: "Test",
          LastName: "User",
          Email: "<EMAIL>",
          PhoneNumber: "+49 **********",
          City: "Berlin",
          DesiredPosition: "Test Position",
          EmploymentType: "full_time",
          Job1_Title: "Previous Job",
          Job1_Company: "Previous Company",
          Job1_Duration: "1 year",
          Job1_Tasks: "Test tasks",
          EducationLevel: "bachelors_degree",
          GermanLevel: "native_speaker",
          OtherLanguages: "English",
          DrivingLicense: "class_b",
          SkillsAndCerts: "Test skills",
          formType: "worker"
        }
      },
      {
        name: "Driving License - Class C1",
        data: {
          FirstName: "Test",
          LastName: "User",
          Email: "<EMAIL>",
          PhoneNumber: "+49 **********",
          City: "Berlin",
          DesiredPosition: "Test Position",
          EmploymentType: "full_time",
          Job1_Title: "Previous Job",
          Job1_Company: "Previous Company",
          Job1_Duration: "1 year",
          Job1_Tasks: "Test tasks",
          EducationLevel: "bachelors_degree",
          GermanLevel: "b2",
          OtherLanguages: "English",
          DrivingLicense: "class_c1",
          SkillsAndCerts: "Test skills",
          formType: "worker"
        }
      },
      {
        name: "Driving License - Forklift License",
        data: {
          FirstName: "Test",
          LastName: "User",
          Email: "<EMAIL>",
          PhoneNumber: "+49 **********",
          City: "Berlin",
          DesiredPosition: "Test Position",
          EmploymentType: "full_time",
          Job1_Title: "Previous Job",
          Job1_Company: "Previous Company",
          Job1_Duration: "1 year",
          Job1_Tasks: "Test tasks",
          EducationLevel: "bachelors_degree",
          GermanLevel: "b2",
          OtherLanguages: "English",
          DrivingLicense: "forklift_license",
          SkillsAndCerts: "Test skills",
          formType: "worker"
        }
      }
    ];

    // Test cases for company form dropdowns
    const companyTestCases = [
      {
        name: "Employment Model - Full-time",
        data: {
          CompanyName: "Test Company GmbH",
          ContactPerson: "Test Person",
          ContactEmail: "<EMAIL>",
          ContactPhone: "+49 **********",
          CompanyWebsite: "https://test.com",
          NeededPositions: "Test Position",
          NumberOfVacancies: "1",
          WorkLocation: "Berlin",
          RequiredSkills: "Test skills",
          EmploymentModel: "full_time",
          Urgency: "urgent",
          JobDescription: "Test job description",
          formType: "company"
        }
      },
      {
        name: "Employment Model - Part-time",
        data: {
          CompanyName: "Test Company GmbH",
          ContactPerson: "Test Person",
          ContactEmail: "<EMAIL>",
          ContactPhone: "+49 **********",
          CompanyWebsite: "https://test.com",
          NeededPositions: "Test Position",
          NumberOfVacancies: "1",
          WorkLocation: "Berlin",
          RequiredSkills: "Test skills",
          EmploymentModel: "part_time",
          Urgency: "urgent",
          JobDescription: "Test job description",
          formType: "company"
        }
      },
      {
        name: "Employment Model - Contract",
        data: {
          CompanyName: "Test Company GmbH",
          ContactPerson: "Test Person",
          ContactEmail: "<EMAIL>",
          ContactPhone: "+49 **********",
          CompanyWebsite: "https://test.com",
          NeededPositions: "Test Position",
          NumberOfVacancies: "1",
          WorkLocation: "Berlin",
          RequiredSkills: "Test skills",
          EmploymentModel: "contract",
          Urgency: "urgent",
          JobDescription: "Test job description",
          formType: "company"
        }
      },
      {
        name: "Test Urgency Values",
        data: {
          CompanyName: "Test Company GmbH",
          ContactPerson: "Test Person",
          ContactEmail: "<EMAIL>",
          ContactPhone: "+49 **********",
          CompanyWebsite: "https://test.com",
          NeededPositions: "Test Position",
          NumberOfVacancies: "1",
          WorkLocation: "Berlin",
          RequiredSkills: "Test skills",
          EmploymentModel: "full_time",
          Urgency: "immediate",
          JobDescription: "Test job description",
          formType: "company"
        }
      }
    ];

    const allTestCases = [...workerTestCases, ...companyTestCases];
    const results = [];

    for (const testCase of allTestCases) {
      try {
        // Choose the correct table based on form type
        const tableId = testCase.data.formType === 'worker' ? workerTableId : companyTableId;
        const response = await fetch(`${baseUrl}/api/v2/tables/${tableId}/records`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'xc-token': nocoDBToken,
          },
          body: JSON.stringify(testCase.data),
        });

        const responseText = await response.text();
        
        results.push({
          testName: testCase.name,
          success: response.ok,
          status: response.status,
          statusText: response.statusText,
          response: responseText.substring(0, 200),
          testedValue: testCase.data.EducationLevel || testCase.data.GermanLevel || testCase.data.EmploymentModel || testCase.data.Urgency || testCase.data.DrivingLicense,
          formType: testCase.data.formType,
          tableId: tableId
        });

        // If successful, we should delete the test record to avoid cluttering the database
        if (response.ok) {
          try {
            const responseData = JSON.parse(responseText);
            if (responseData.Id) {
              await fetch(`${baseUrl}/api/v2/tables/${tableId}/records/${responseData.Id}`, {
                method: 'DELETE',
                headers: {
                  'xc-token': nocoDBToken,
                },
              });
            }
          } catch (deleteError) {
            console.log('Could not delete test record:', deleteError.message);
          }
        }

      } catch (error) {
        results.push({
          testName: testCase.name,
          success: false,
          error: error.message,
          testedValue: testCase.data.EducationLevel || testCase.data.GermanLevel || testCase.data.EmploymentModel || testCase.data.Urgency || testCase.data.DrivingLicense,
          formType: testCase.data.formType
        });
      }
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Dropdown validation tests completed',
        timestamp: new Date().toISOString(),
        results: results,
        summary: {
          total: results.length,
          successful: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length
        }
      })
    };

  } catch (error) {
    console.error('Test error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
