// Avatar upload function for Netlify
const { Storage } = require('@google-cloud/storage');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const fs = require('fs');
const path = require('path');
const os = require('os');

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Environment variables validation
const validateEnvironment = () => {
  const required = [
    'GCS_PROJECT_ID',
    'GCS_BUCKET_NAME',
    'GCS_SERVICE_ACCOUNT_EMAIL',
    'GCS_PRIVATE_KEY',
    'NOCODB_URL',
    'NOCODB_TOKEN'
  ];

  const missing = required.filter(key => !process.env[key]);
  if (missing.length > 0) {
    console.error('Missing environment variables:', missing);
    console.error('Available env vars:', Object.keys(process.env).filter(key =>
      key.includes('GCS') || key.includes('NOCODB')
    ));
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  console.log('Environment validation passed. All required variables present.');
};

// Initialize Google Cloud Storage
const initializeStorage = () => {
  const gcsProjectId = process.env.GCS_PROJECT_ID;
  const gcsBucketName = process.env.GCS_BUCKET_NAME;

  console.log('Initializing GCS with:', {
    projectId: gcsProjectId,
    bucketName: gcsBucketName,
    serviceAccountEmail: process.env.GCS_SERVICE_ACCOUNT_EMAIL,
    privateKeyLength: process.env.GCS_PRIVATE_KEY ? process.env.GCS_PRIVATE_KEY.length : 0
  });

  // Handle private key formatting - it might be stored with escaped newlines
  let privateKey = process.env.GCS_PRIVATE_KEY;
  if (privateKey) {
    // Replace escaped newlines with actual newlines
    privateKey = privateKey.replace(/\\n/g, '\n');
    console.log('Private key formatted, length:', privateKey.length);
  }

  const storage = new Storage({
    projectId: gcsProjectId,
    credentials: {
      type: 'service_account',
      project_id: gcsProjectId,
      client_email: process.env.GCS_SERVICE_ACCOUNT_EMAIL,
      private_key: privateKey,
    },
  });

  console.log('GCS storage initialized successfully');

  return { storage, bucket: storage.bucket(gcsBucketName), bucketName: gcsBucketName };
};

// Parse JSON data with base64 encoded file
const parseJsonData = (event) => {
  try {
    const data = JSON.parse(event.body);

    if (!data.fileData || !data.fileName || !data.recordId) {
      throw new Error('Missing required fields: fileData, fileName, recordId');
    }

    // Validate file size (base64 encoded, so actual size is ~75% of encoded size)
    const estimatedSize = (data.fileData.length * 3) / 4;
    const maxSize = 2 * 1024 * 1024; // 2MB limit
    const minSize = 100; // 100 bytes minimum (reduced for test compatibility)

    console.log('File size validation:', {
      base64Length: data.fileData.length,
      estimatedSize: estimatedSize,
      minSize: minSize,
      maxSize: maxSize
    });

    if (estimatedSize > maxSize) {
      throw new Error('File too large. Maximum size is 2MB.');
    }

    if (estimatedSize < minSize) {
      throw new Error(`File too small. Minimum size is ${minSize} bytes, got ${estimatedSize} bytes.`);
    }

    // Validate file type - only allow specific image formats
    const mimeTypeMatch = data.fileData.match(/^data:([^;]+);base64,/);
    if (!mimeTypeMatch) {
      throw new Error('Invalid file format. Please upload a valid image.');
    }

    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    const mimeType = mimeTypeMatch[1].toLowerCase();
    if (!allowedTypes.includes(mimeType)) {
      throw new Error('Invalid file type. Only JPG and PNG images are allowed.');
    }

    const base64Data = data.fileData.replace(/^data:[^;]+;base64,/, '');

    return {
      recordId: data.recordId,
      fileName: data.fileName,
      mimeType: mimeType,
      fileBuffer: Buffer.from(base64Data, 'base64')
    };
  } catch (error) {
    throw new Error(`Failed to parse request data: ${error.message}`);
  }
};

// Upload buffer to Google Cloud Storage
const uploadToGCS = async (fileData, bucket, bucketName) => {
  // Generate unique filename
  const timestamp = Date.now();
  const fileExtension = fileData.fileName.split('.').pop() || 'jpg';
  const uniqueFileName = `avatars/avatar-${timestamp}.${fileExtension}`;

  console.log('Uploading file to GCS:', {
    originalName: fileData.fileName,
    uniqueName: uniqueFileName,
    size: fileData.fileBuffer.length,
    mimetype: fileData.mimeType
  });

  // Create a temporary file from the buffer
  const tempFilePath = path.join(os.tmpdir(), `upload-${timestamp}.${fileExtension}`);
  fs.writeFileSync(tempFilePath, fileData.fileBuffer);

  try {
    console.log('Starting GCS upload...');

    // Skip bucket existence check to avoid permission issues
    // The upload will fail if bucket doesn't exist anyway
    console.log('⏭️ Skipping bucket existence check (permission optimization)');

    // Upload file to GCS and make it publicly accessible
    console.log('Uploading file to bucket...');
    await bucket.upload(tempFilePath, {
      destination: uniqueFileName,
      public: true, // Make file publicly accessible
      metadata: {
        contentType: fileData.mimeType,
        // Set long-term browser cache for better performance
        cacheControl: 'public, max-age=31536000', // 1 year cache
      },
    });
    console.log('✅ File uploaded to GCS successfully');

    // Construct public URL
    const publicUrl = `https://storage.googleapis.com/${bucketName}/${uniqueFileName}`;

    console.log('File uploaded successfully:', publicUrl);
    return publicUrl;
  } finally {
    // Clean up temporary file
    try {
      fs.unlinkSync(tempFilePath);
    } catch (cleanupError) {
      console.warn('Failed to cleanup temp file:', cleanupError);
    }
  }
};

// Update NocoDB record with avatar URL
const updateNocoDBRecord = async (recordId, avatarUrl) => {
  const nocoDBUrl = process.env.NOCODB_URL;
  const nocoDBToken = process.env.NOCODB_TOKEN;

  // Worker table ID (from existing code)
  const nocoTableId = 'mcv0hz2nurqap37';

  const baseUrl = nocoDBUrl.endsWith('/') ? nocoDBUrl.slice(0, -1) : nocoDBUrl;
  const updateUrl = `${baseUrl}/api/v2/tables/${nocoTableId}/records`;

  console.log('Updating NocoDB record:', {
    recordId,
    avatarUrl,
    updateUrl,
    tableId: nocoTableId
  });

  // Try different possible field names for avatar based on common NocoDB naming patterns
  const possibleFieldNames = [
    'Avatar', 'avatar', 'AvatarUrl', 'avatar_url',
    'ProfilePicture', 'profile_picture', 'Photo', 'photo',
    'Image', 'image', 'Picture', 'picture'
  ];

  for (const fieldName of possibleFieldNames) {
    console.log(`Trying to update field: ${fieldName}`);

    const response = await fetch(updateUrl, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'xc-token': nocoDBToken,
      },
      body: JSON.stringify([{
        Id: recordId,
        [fieldName]: avatarUrl
      }]),
    });

    const responseText = await response.text();
    console.log(`Response for field ${fieldName}:`, {
      status: response.status,
      statusText: response.statusText,
      response: responseText
    });

    if (response.ok) {
      const result = JSON.parse(responseText);
      console.log(`NocoDB record updated successfully with field ${fieldName}:`, result);
      return result;
    }
  }

  // If all field names failed, throw error
  throw new Error(`Failed to update NocoDB record with any avatar field name. Last response: ${responseText}`);
};

// Main handler function
exports.handler = async (event) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers };
  }

  // Only accept POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method Not Allowed' })
    };
  }

  try {
    console.log('=== AVATAR UPLOAD REQUEST ===');
    console.log('Timestamp:', new Date().toISOString());
    console.log('Request method:', event.httpMethod);
    console.log('Request body length:', event.body ? event.body.length : 'no body');
    console.log('User agent:', event.headers?.['user-agent'] || 'unknown');

    // Validate environment variables first
    try {
      validateEnvironment();
    } catch (envError) {
      console.error('Environment validation failed:', envError.message);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing required environment variables for avatar upload',
          missingVars: envError.message,
          step: 'environment_validation'
        })
      };
    }

    // Parse JSON data with base64 file
    let fileData;
    try {
      fileData = parseJsonData(event);
      console.log('File data parsed successfully:', {
        recordId: fileData.recordId,
        fileName: fileData.fileName,
        mimeType: fileData.mimeType,
        fileSize: fileData.fileBuffer.length
      });
    } catch (parseError) {
      console.error('Failed to parse request data:', parseError.message);
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({
          error: 'Invalid request data',
          details: parseError.message,
          step: 'data_parsing'
        })
      };
    }

    // Initialize Google Cloud Storage
    let bucket, bucketName;
    try {
      const gcsResult = initializeStorage();
      bucket = gcsResult.bucket;
      bucketName = gcsResult.bucketName;
      console.log('Google Cloud Storage initialized successfully');
    } catch (gcsError) {
      console.error('Failed to initialize Google Cloud Storage:', gcsError.message);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Storage initialization failed',
          details: gcsError.message,
          step: 'gcs_initialization'
        })
      };
    }

    // Upload file to Google Cloud Storage
    let avatarUrl;
    try {
      avatarUrl = await uploadToGCS(fileData, bucket, bucketName);
      console.log('File uploaded to GCS successfully:', avatarUrl);
    } catch (uploadError) {
      console.error('Failed to upload to Google Cloud Storage:', uploadError.message);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'File upload failed',
          details: uploadError.message,
          step: 'gcs_upload'
        })
      };
    }

    // Update NocoDB record with avatar URL
    let updateResult;
    try {
      updateResult = await updateNocoDBRecord(fileData.recordId, avatarUrl);
      console.log('NocoDB record updated successfully');
    } catch (updateError) {
      console.error('Failed to update NocoDB record:', updateError.message);
      // Even if NocoDB update fails, the file was uploaded successfully
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          message: 'Avatar uploaded to storage but failed to update database record',
          avatarUrl,
          recordId: fileData.recordId,
          warning: 'Database update failed',
          details: updateError.message,
          step: 'nocodb_update'
        })
      };
    }

    // Return success response
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Avatar uploaded successfully',
        avatarUrl,
        recordId: fileData.recordId,
        nocodbResponse: updateResult
      })
    };

  } catch (error) {
    console.error('Unexpected avatar upload error:', error);

    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Unexpected error during avatar upload',
        details: error.message,
        stack: error.stack
      })
    };
  }
};
