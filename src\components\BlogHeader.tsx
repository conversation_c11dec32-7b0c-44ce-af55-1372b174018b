import React from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useI18n } from '@/contexts/I18nContext';
import { useNavigation } from '@/contexts/NavigationContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useAssets } from '@/hooks/useAssets';
import { useFormNavigation } from '@/hooks/useFormNavigation';
import LanguageSwitcher from './LanguageSwitcher';

/**
 * BlogHeader Component
 * Theme-aware header specifically designed for blog pages
 * Includes theme toggle functionality and audience-based styling
 */
const BlogHeader: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useI18n();
  const { markInternalNavigation } = useNavigation();
  const { currentTheme } = useTheme();
  const { getLogo } = useAssets();
  const { scrollToFormSection } = useFormNavigation();
  const [searchParams, setSearchParams] = useSearchParams();

  // Determine current audience from URL params or theme context
  const urlAudience = searchParams.get('audience');
  const currentAudience = urlAudience || (currentTheme === 'employer' ? 'Employer' : 'Job-seeker');
  const isEmployerAudience = currentAudience === 'Employer';

  // Get appropriate logo based on current audience
  const logoSrc = isEmployerAudience ? getLogo('dark') : getLogo('main');

  // Handle theme toggle functionality
  const handleThemeToggle = () => {
    markInternalNavigation('header-toggle');

    // Determine new audience
    const newAudience = isEmployerAudience ? 'Job-seeker' : 'Employer';

    // Update URL parameters
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('audience', newAudience);

    // Update the current page URL with new audience
    setSearchParams(newSearchParams);
  };

  // Handle navigation to home - audience-aware routing
  const handleHomeClick = () => {
    markInternalNavigation('internal');

    // Navigate to appropriate home page based on current audience
    const homePage = isEmployerAudience ? '/hiring' : '/';
    navigate(homePage);
  };

  // Handle Get Started CTA - navigate to appropriate main page and scroll to form
  const handleGetStartedClick = () => {
    markInternalNavigation('internal');

    // Navigate to appropriate main page based on current audience
    const targetPage = isEmployerAudience ? '/hiring' : '/';
    navigate(targetPage);

    // Scroll to form section after navigation
    setTimeout(() => {
      scrollToFormSection();
    }, 100);
  };

  // Get theme-appropriate header styling
  const getHeaderStyling = () => {
    const baseClasses = "fixed top-0 left-0 right-0 z-[200] backdrop-blur-md";

    if (isEmployerAudience) {
      // Employer theme: Professional teal with darker styling
      return `${baseClasses} bg-primary shadow-sm`;
    }

    // Job seeker theme: Warm styling
    return `${baseClasses} bg-primary shadow-sm`;
  };

  return (
    <header className={getHeaderStyling()}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 bg-pr">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <button
              onClick={handleHomeClick}
              className="flex items-center transition-smooth hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2 rounded-lg p-1"
              aria-label={t('navigation.home')}
            >
              <img
                src={logoSrc}
                alt="Sohnus - AI-powered job platform"
                className="h-8 md:h-10 w-auto"
              />
            </button>
          </div>

          {/* Navigation */}
          <div className="flex items-center gap-2 sm:gap-4">
            {/* Current Audience Indicator (hidden on mobile) */}


            {/* Home Button */}
            <Button
              size="sm"
              variant="outline"
              onClick={handleHomeClick}
              aria-label={t('navigation.home')}
            >
              {t('navigation.home')}
            </Button>

            {/* Theme Toggle Button */}
            <Button
              size="sm"
              variant="outline"
              onClick={handleThemeToggle}
              aria-label={isEmployerAudience ? "Switch to job seeker blog" : "Switch to employer blog"}
            >
              <span className="hidden sm:inline">
                {isEmployerAudience ? t('navigation.toggle_job_seeking') : t('navigation.toggle_hiring')}
              </span>
              <span className="sm:hidden">
                {isEmployerAudience ? t('navigation.toggle_job_seeking_short') : t('navigation.toggle_hiring_short')}
              </span>
            </Button>

            {/* Language Switcher */}
            <LanguageSwitcher />

            {/* Get Started CTA Button */}
            <Button
              size="sm"
              variant="secondary"
              onClick={handleGetStartedClick}
              aria-label="Get started with Sohnus"
            >
              <span className="hidden sm:inline">{t('forms.title')}</span>
              <span className="sm:hidden">Start</span>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default BlogHeader;
