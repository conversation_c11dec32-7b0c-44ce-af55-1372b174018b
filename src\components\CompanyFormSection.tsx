import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { Send, Loader2 } from "lucide-react";
import { useI18n, useFormOptions } from "@/contexts/I18nContext";
import { useDesignTokens } from "@/hooks/useDesignTokens";

const CompanyFormSection = () => {
  const { t, language } = useI18n();
  const formOptions = useFormOptions();
  const { tokens, themeClasses, combineClasses } = useDesignTokens();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    CompanyName: "",
    ContactPerson: "",
    ContactEmail: "",
    ContactPhone: "",
    CompanyWebsite: "",
    NeededPositions: "",
    NumberOfVacancies: "",
    WorkLocation: "",
    RequiredSkills: "",
    EmploymentModel: "",
    Urgency: "",
    JobDescription: "",
    Status: "FormSubmit",
    Language: language,
    formType: "company"
  });
  const { toast } = useToast();

  // Sync language field when user changes language
  useEffect(() => {
    setFormData(prev => ({ ...prev, Language: language }));
  }, [language]);

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digit characters except + at the beginning
    const cleaned = value.replace(/[^\d+]/g, '');

    // If it starts with +49, format more flexibly
    if (cleaned.startsWith('+49')) {
      const digits = cleaned.slice(3);
      if (digits.length <= 2) {
        return `+49 ${digits}`;
      } else if (digits.length <= 5) {
        return `+49 ${digits.slice(0, 2)} ${digits.slice(2)}`;
      } else if (digits.length <= 8) {
        return `+49 ${digits.slice(0, 3)} ${digits.slice(3)}`;
      } else if (digits.length <= 11) {
        return `+49 ${digits.slice(0, 4)} ${digits.slice(4)}`;
      } else {
        return `+49 ${digits.slice(0, 5)} ${digits.slice(5, 17)}`;
      }
    }

    // If it starts with 0, assume German number and convert to +49
    if (cleaned.startsWith('0')) {
      const digits = cleaned.slice(1);
      if (digits.length <= 2) {
        return `+49 ${digits}`;
      } else if (digits.length <= 5) {
        return `+49 ${digits.slice(0, 2)} ${digits.slice(2)}`;
      } else if (digits.length <= 8) {
        return `+49 ${digits.slice(0, 3)} ${digits.slice(3)}`;
      } else if (digits.length <= 11) {
        return `+49 ${digits.slice(0, 4)} ${digits.slice(4)}`;
      } else {
        return `+49 ${digits.slice(0, 5)} ${digits.slice(5, 17)}`;
      }
    }

    // For other formats, just clean and return
    return cleaned;
  };

  const handleInputChange = (field: string, value: string) => {
    if (field === 'ContactPhone') {
      const formatted = formatPhoneNumber(value);
      setFormData(prev => ({ ...prev, [field]: formatted }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const validateForm = () => {
    // More flexible phone number validation for German numbers
    const phoneRegex = /^\+49\s\d{2,5}\s\d{4,12}$/;
    if (!phoneRegex.test(formData.ContactPhone)) {
      toast({
        title: t('common.error'),
        description: "Please enter a valid German phone number (e.g., +49 **********)",
        variant: "destructive",
      });
      return false;
    }

    // Check required fields
    const requiredFields = ['CompanyName', 'ContactPerson', 'ContactEmail', 'ContactPhone'];
    for (const field of requiredFields) {
      if (!formData[field as keyof typeof formData].trim()) {
        toast({
          title: t('common.error'),
          description: `Please fill in the ${field.replace(/([A-Z])/g, ' $1').toLowerCase()} field.`,
          variant: "destructive",
        });
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/submit-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ...formData, formType: "company" }),
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: t('company_form.success_title'),
          description: t('company_form.success_description'),
          variant: "success",
        });
        // Reset form
        setFormData({
          CompanyName: "",
          ContactPerson: "",
          ContactEmail: "",
          ContactPhone: "",
          CompanyWebsite: "",
          NeededPositions: "",
          NumberOfVacancies: "",
          WorkLocation: "",
          RequiredSkills: "",
          EmploymentModel: "",
          Urgency: "",
          JobDescription: "",
          Status: "FormSubmit",
          Language: language,
          formType: "company"
        });
      } else {
        throw new Error(result.message || 'Submission failed');
      }
    } catch (error) {
      toast({
        title: t('common.error'),
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className={combineClasses(tokens.card.elevated, themeClasses.formSection)}>
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-2xl">{t('company_form.title')}</CardTitle>
            <p className="text-muted-foreground">{t('company_form.subtitle')}</p>
            <p className="text-sm text-muted-foreground mt-2">
              <span className="text-red-500">*</span> {t('common.required')}
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="companyName">{t('company_form.fields.company_name')} <span className="text-red-500">*</span></Label>
                <Input
                  id="companyName"
                  value={formData.CompanyName}
                  onChange={(e) => handleInputChange('CompanyName', e.target.value)}
                  placeholder={t('company_form.placeholders.company_name')}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="contactPerson">{t('company_form.fields.contact_person')} <span className="text-red-500">*</span></Label>
                  <Input
                    id="contactPerson"
                    value={formData.ContactPerson}
                    onChange={(e) => handleInputChange('ContactPerson', e.target.value)}
                    placeholder={t('company_form.placeholders.contact_person')}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactEmail">{t('company_form.fields.contact_email')} <span className="text-red-500">*</span></Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={formData.ContactEmail}
                    onChange={(e) => handleInputChange('ContactEmail', e.target.value)}
                    placeholder={t('company_form.placeholders.contact_email')}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="contactPhone">{t('company_form.fields.contact_phone')} <span className="text-red-500">*</span></Label>
                  <Input
                    id="contactPhone"
                    type="tel"
                    value={formData.ContactPhone}
                    onChange={(e) => handleInputChange('ContactPhone', e.target.value)}
                    placeholder={t('company_form.placeholders.contact_phone')}
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    German format: +49 ********** (automatically formatted)
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="companyWebsite">{t('company_form.fields.company_website')}</Label>
                  <Input
                    id="companyWebsite"
                    type="url"
                    value={formData.CompanyWebsite}
                    onChange={(e) => handleInputChange('CompanyWebsite', e.target.value)}
                    placeholder={t('company_form.placeholders.company_website')}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="neededPositions">{t('company_form.fields.needed_positions')} <span className="text-red-500">*</span></Label>
                <Input
                  id="neededPositions"
                  value={formData.NeededPositions}
                  onChange={(e) => handleInputChange('NeededPositions', e.target.value)}
                  placeholder={t('company_form.placeholders.needed_positions')}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="numberOfVacancies">{t('company_form.fields.number_of_vacancies')} <span className="text-red-500">*</span></Label>
                  <Input
                    id="numberOfVacancies"
                    type="number"
                    min="1"
                    value={formData.NumberOfVacancies}
                    onChange={(e) => handleInputChange('NumberOfVacancies', e.target.value)}
                    placeholder={t('company_form.placeholders.number_of_vacancies')}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="workLocation">{t('company_form.fields.work_location')} <span className="text-red-500">*</span></Label>
                  <Input
                    id="workLocation"
                    value={formData.WorkLocation}
                    onChange={(e) => handleInputChange('WorkLocation', e.target.value)}
                    placeholder={t('company_form.placeholders.work_location')}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="requiredSkills">{t('company_form.fields.required_skills')} <span className="text-red-500">*</span></Label>
                <Textarea
                  id="requiredSkills"
                  value={formData.RequiredSkills}
                  onChange={(e) => handleInputChange('RequiredSkills', e.target.value)}
                  placeholder={t('company_form.placeholders.required_skills')}
                  className="min-h-[100px]"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="employmentModel">{t('company_form.fields.employment_model')} <span className="text-red-500">*</span></Label>
                  <Select value={formData.EmploymentModel} onValueChange={(value) => handleInputChange('EmploymentModel', value)} required>
                    <SelectTrigger>
                      <SelectValue placeholder={t('company_form.placeholders.employment_model')} />
                    </SelectTrigger>
                    <SelectContent>
                      {formOptions.companyEmploymentModels.map((option) => (
                        <SelectItem key={option.key} value={option.key}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="urgency">{t('company_form.fields.urgency')} <span className="text-red-500">*</span></Label>
                  <Select value={formData.Urgency} onValueChange={(value) => handleInputChange('Urgency', value)} required>
                    <SelectTrigger>
                      <SelectValue placeholder={t('company_form.placeholders.urgency')} />
                    </SelectTrigger>
                    <SelectContent>
                      {formOptions.urgencyLevels.map((option) => (
                        <SelectItem key={option.key} value={option.key}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="jobDescription">{t('company_form.fields.job_description')} <span className="text-red-500">*</span></Label>
                <Textarea
                  id="jobDescription"
                  value={formData.JobDescription}
                  onChange={(e) => handleInputChange('JobDescription', e.target.value)}
                  placeholder={t('company_form.placeholders.job_description')}
                  className="min-h-[120px]"
                  required
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <Button
                  type="submit"
                  size="lg"
                  className={combineClasses("flex-1", tokens.button.primary, themeClasses.ctaButton)}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4 mr-2" />
                  )}
                  {isSubmitting ? t('common.submitting') : t('common.submit')}
                </Button>
              </div>

              <div className="text-center pt-4">
                <p className="text-sm text-muted-foreground">
                  {t('forms.footer_agreement')}{" "}
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm"
                    onClick={() => window.open(language === 'de' ? '/privacy-policy-de' : '/privacy-policy', '_blank')}
                  >
                    {t('navigation.privacy_policy')}
                  </Button>{" "}
                  {t('forms.and')}{" "}
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm"
                    onClick={() => window.open(language === 'de' ? '/terms-of-service-de' : '/terms-of-service', '_blank')}
                  >
                    {t('navigation.terms_of_service')}
                  </Button>
                </p>
              </div>
            </form>
          </CardContent>
        </Card>
  );
};

export default CompanyFormSection;
