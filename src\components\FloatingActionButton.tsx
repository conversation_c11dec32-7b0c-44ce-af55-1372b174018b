import React from 'react';
import { useLocation } from 'react-router-dom';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { UserPlus, ArrowUp } from "lucide-react";

interface FloatingActionButtonProps {
  onFormSectionClick?: () => void;
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({ onFormSectionClick }) => {
  const location = useLocation();
  const isHiringPage = location.pathname === '/hiring';

  const handleClick = () => {
    if (onFormSectionClick) {
      onFormSectionClick();
    }
  };

  return (
    <div className="fixed bottom-4 right-0 sm:bottom-6 sm:right-6 z-40">
          <Button
            onClick={handleClick}
            size="lg"
            className="h-12 sm:h-14 px-4 sm:px-6 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 active:scale-95 bg-primary hover:bg-primary/90 text-primary-foreground font-semibold text-sm sm:text-base group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            aria-label={`Sign up now - scroll to ${isHiringPage ? 'company' : 'worker'} form`}
          >
        <UserPlus className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 transition-transform group-hover:scale-110" />
        <span className="hidden sm:inline">Sign Up Now</span>
        <span className="sm:hidden">Sign Up</span>
        <ArrowUp className="h-3 w-3 sm:h-4 sm:w-4 ml-1 sm:ml-2 transition-transform group-hover:-translate-y-0.5" />
      </Button>
    </div>
  );
};

export default FloatingActionButton;
