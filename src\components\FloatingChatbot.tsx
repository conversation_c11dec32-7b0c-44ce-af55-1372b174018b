import React, { useState, useEffect, useRef } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MessageCircle, X, Send, Loader2, Rocket, MessageSquare, Phone, Facebook, UserPlus } from "lucide-react";
import { useI18n } from "@/contexts/I18nContext";
import { useToast } from "@/components/ui/use-toast";
import { useFormNavigation } from "@/hooks/useFormNavigation";

interface ChatMessage {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  options?: ChatOption[];
}

interface ChatOption {
  id: string;
  text: string;
  icon?: React.ReactNode;
  action: () => void;
}

interface ContactInfo {
  phone?: string;
  facebook?: string;
  wechat?: string;
  contactMethod?: 'whatsapp' | 'facebook' | 'wechat';
}

interface ContactStep {
  type: 'phone' | 'facebook' | 'wechat';
  label: string;
  placeholder: string;
}

const FloatingChatbot: React.FC = () => {
  const { t, language } = useI18n();
  const { toast } = useToast();
  const { scrollToFormSection } = useFormNavigation();
  const [isOpen, setIsOpen] = useState(false);
  const [hasAutoOpened, setHasAutoOpened] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [contactInfo, setContactInfo] = useState<ContactInfo>({});
  const [chatStep, setChatStep] = useState<'greeting' | 'choice' | 'contact' | 'contact_options' | 'confirm' | 'complete'>('greeting');
  const [currentContactStep, setCurrentContactStep] = useState<ContactStep | null>(null);
  const [availableContactSteps, setAvailableContactSteps] = useState<ContactStep[]>([]);
  const [messageCounter, setMessageCounter] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Auto-popup after 10 seconds on job seeker page
  useEffect(() => {
    if (!hasAutoOpened && !isOpen) {
      const timer = setTimeout(() => {
        setHasAutoOpened(true);
        setIsOpen(true);
        addBotMessage(getGreetingMessage());
        // Show options immediately after greeting
        setTimeout(() => {
          showInitialOptions();
        }, 500);
      }, 10000); // 10 seconds

      return () => clearTimeout(timer);
    }
  }, [hasAutoOpened, isOpen, language]);

  const getGreetingMessage = () => {
    const greetings = {
      en: "Hey there! 👋 I'm here to help you find your dream job in Germany! What brings you here today?",
      de: "Hallo! 👋 Ich bin hier, um dir bei der Jobsuche in Deutschland zu helfen! Was führt dich heute hierher?",
      zh: "你好！👋 我是来帮助你在德国找到理想工作的！今天是什么让你来到这里的呢？",
      es: "¡Hola! 👋 ¡Estoy aquí para ayudarte a encontrar tu trabajo soñado en Alemania! ¿Qué te trae aquí hoy?"
    };
    return greetings[language as keyof typeof greetings] || greetings.en;
  };

  const addBotMessage = (text: string, options?: ChatOption[]) => {
    setMessageCounter(prev => {
      const newCounter = prev + 1;
      const message: ChatMessage = {
        id: `${Date.now()}-${newCounter}`,
        text,
        isBot: true,
        timestamp: new Date(),
        options
      };
      setMessages(prevMessages => [...prevMessages, message]);
      return newCounter;
    });
  };

  const addUserMessage = (text: string) => {
    setMessageCounter(prev => {
      const newCounter = prev + 1;
      const message: ChatMessage = {
        id: `${Date.now()}-${newCounter}`,
        text,
        isBot: false,
        timestamp: new Date()
      };
      setMessages(prevMessages => [...prevMessages, message]);
      return newCounter;
    });
  };

  // Google Analytics 4 tracking function
  const trackChatbotChoice = (selection: string, label: string) => {
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'chatbot_choice', {
        'event_category': 'Chatbot Interaction',
        'event_label': label,
        'selection': selection,
        'language': language
      });
    }
  };

  const handleOpenChat = () => {
    setIsOpen(true);
    if (messages.length === 0) {
      addBotMessage(getGreetingMessage());
      setTimeout(() => {
        showInitialOptions();
      }, 1000);
    }
  };

  const showInitialOptions = () => {
    const optionTexts = {
      en: {
        cv: "Create my CV now (recommended!)",
        chat: "Just chat a bit to know more"
      },
      de: {
        cv: "Meinen Lebenslauf jetzt erstellen (empfohlen!)",
        chat: "Erstmal nur ein bisschen chatten"
      },
      zh: {
        cv: "立即创建我的简历（推荐！）",
        chat: "先聊聊了解更多"
      },
      es: {
        cv: "Crear mi CV ahora (¡recomendado!)",
        chat: "Solo chatear un poco para saber más"
      }
    };

    const texts = optionTexts[language as keyof typeof optionTexts] || optionTexts.en;

    const options: ChatOption[] = [
      {
        id: 'create_cv',
        text: texts.cv,
        icon: <Rocket className="h-4 w-4" />,
        action: () => {
          // Track GA4 event
          trackChatbotChoice('create_cv', 'Create CV');

          addUserMessage(texts.cv);
          setTimeout(() => {
            const responses = {
              en: "Perfect choice! 🎉 Let me redirect you to our form where you can create your professional CV. It only takes a few minutes!",
              de: "Perfekte Wahl! 🎉 Ich leite dich zu unserem Formular weiter, wo du deinen professionellen Lebenslauf erstellen kannst. Dauert nur ein paar Minuten!",
              zh: "完美的选择！🎉 让我带你到我们的表单，在那里你可以创建你的专业简历。只需要几分钟！",
              es: "¡Elección perfecta! 🎉 Te redirijo a nuestro formulario donde puedes crear tu CV profesional. ¡Solo toma unos minutos!"
            };
            addBotMessage(responses[language as keyof typeof responses] || responses.en);
            setTimeout(() => {
              scrollToFormSection();
              // Show chat option again after redirecting to form
              setTimeout(() => {
                showChatOptionAfterCV();
              }, 1000);
            }, 2000);
          }, 500);
        }
      },
      {
        id: 'just_chat',
        text: texts.chat,
        icon: <MessageSquare className="h-4 w-4" />,
        action: () => {
          // Track GA4 event
          trackChatbotChoice('chat_first', 'Chat First');

          addUserMessage(texts.chat);
          setChatStep('choice');
          setTimeout(() => {
            showContactOptions();
          }, 500);
        }
      }
    ];

    const questionTexts = {
      en: "What would you like to do?",
      de: "Was möchtest du tun?",
      zh: "你想做什么？",
      es: "¿Qué te gustaría hacer?"
    };

    addBotMessage(questionTexts[language as keyof typeof questionTexts] || questionTexts.en, options);
  };

  const showChatOptionAfterCV = () => {
    const chatOptionTexts = {
      en: "If you don't want to create your CV right now, you can also connect with us and chat! 😊",
      de: "Falls du deinen Lebenslauf gerade nicht erstellen möchtest, kannst du auch mit uns in Kontakt treten und chatten! 😊",
      zh: "如果你现在不想创建简历，也可以联系我们聊聊！😊",
      es: "Si no quieres crear tu CV ahora mismo, ¡también puedes conectar con nosotros y chatear! 😊"
    };

    const chatButtonTexts = {
      en: "Chat with us instead",
      de: "Stattdessen mit uns chatten",
      zh: "先和我们聊聊",
      es: "Chatear con nosotros en su lugar"
    };

    const options: ChatOption[] = [
      {
        id: 'chat_instead',
        text: chatButtonTexts[language as keyof typeof chatButtonTexts] || chatButtonTexts.en,
        icon: <MessageSquare className="h-4 w-4" />,
        action: () => {
          trackChatbotChoice('chat_instead_of_cv', 'Chat Instead of CV');
          addUserMessage(chatButtonTexts[language as keyof typeof chatButtonTexts] || chatButtonTexts.en);
          setChatStep('choice');
          setTimeout(() => {
            showContactOptions();
          }, 500);
        }
      }
    ];

    addBotMessage(chatOptionTexts[language as keyof typeof chatOptionTexts] || chatOptionTexts.en, options);
  };

  const showContactOptions = () => {
    const isChineseUser = language === 'zh';

    if (isChineseUser) {
      // Chinese users: 3 specific options - WhatsApp direct, WeChat direct, and give contact info
      const responses = {
        zh: "太好了！我可以帮你联系我们的团队。请选择你喜欢的联系方式："
      };

      const options: ChatOption[] = [
        {
          id: 'whatsapp_direct_zh',
          text: "直接通过WhatsApp联系",
          icon: <Phone className="h-4 w-4" />,
          action: async () => {
            trackChatbotChoice('whatsapp_direct_zh', 'WhatsApp Direct (Chinese)');
            addUserMessage("直接通过WhatsApp联系");
            // Pre-filled message: "你好，我从你们的网站过来，想了解求职信息。"
            const message = encodeURIComponent("你好，我从你们的网站过来，想了解求职信息。");
            window.open(`https://wa.me/4915756407589?text=${message}`, '_blank');

            setTimeout(() => {
              addBotMessage("已为你打开WhatsApp！我们会尽快回复你的消息。感谢你的信任！🙏");
              setChatStep('complete');
            }, 1000);
          }
        },
        {
          id: 'wechat_direct_zh',
          text: "直接通过微信联系",
          icon: <MessageCircle className="h-4 w-4" />,
          action: async () => {
            trackChatbotChoice('wechat_direct_zh', 'WeChat Direct (Chinese)');
            addUserMessage("直接通过微信联系");

            setTimeout(() => {
              addBotMessage("请添加我们的微信号：SohnusDE，我们会尽快回复你的消息。感谢你的信任！🙏");
              setChatStep('complete');
            }, 1000);
          }
        },
        {
          id: 'leave_contact_zh',
          text: "留下我的联系方式",
          icon: <UserPlus className="h-4 w-4" />,
          action: () => {
            trackChatbotChoice('leave_contact_zh', 'Leave Contact (Chinese)');
            addUserMessage("留下我的联系方式");
            showContactMethodOptions();
          }
        }
      ];

      addBotMessage(responses.zh, options);
    } else {
      // Non-Chinese users: WhatsApp and Facebook options
      const responses = {
        en: "Great! I can help you connect with our team. We support WhatsApp and Facebook. Choose your preferred way:",
        de: "Super! Ich kann dir helfen, mit unserem Team in Kontakt zu treten. Wir unterstützen WhatsApp und Facebook. Wähle deinen bevorzugten Weg:",
        es: "¡Genial! Puedo ayudarte a conectar con nuestro equipo. Apoyamos WhatsApp y Facebook. Elige tu forma preferida:"
      };

      const options: ChatOption[] = [
        {
          id: 'whatsapp_direct',
          text: language === 'de' ? "Direkt über WhatsApp kontaktieren" :
                language === 'es' ? "Contactar directamente por WhatsApp" :
                "Contact directly via WhatsApp",
          icon: <Phone className="h-4 w-4" />,
          action: async () => {
            trackChatbotChoice('whatsapp_direct', 'WhatsApp Direct');
            const userTexts = {
              en: "Contact directly via WhatsApp",
              de: "Direkt über WhatsApp kontaktieren",
              es: "Contactar directamente por WhatsApp"
            };
            addUserMessage(userTexts[language as keyof typeof userTexts] || userTexts.en);

            // Pre-filled messages for different languages
            const prefilledMessages = {
              en: "Hello, I came from your website and would like to learn about job opportunities.",
              de: "Hallo, ich komme von Ihrer Website und möchte mich über Stellenangebote informieren.",
              es: "Hola, vengo de su sitio web y me gustaría conocer sobre oportunidades de trabajo."
            };
            const message = encodeURIComponent(prefilledMessages[language as keyof typeof prefilledMessages] || prefilledMessages.en);
            window.open(`https://wa.me/4915756407589?text=${message}`, '_blank');

            setTimeout(() => {
              const botResponses = {
                en: "WhatsApp opened for you! We'll reply to your message soon. Thanks for trusting us! 🙏",
                de: "WhatsApp wurde für dich geöffnet! Wir antworten bald auf deine Nachricht. Danke für dein Vertrauen! 🙏",
                es: "¡WhatsApp abierto para ti! Responderemos a tu mensaje pronto. ¡Gracias por confiar en nosotros! 🙏"
              };
              addBotMessage(botResponses[language as keyof typeof botResponses] || botResponses.en);
              setChatStep('complete');
            }, 1000);
          }
        },
        {
          id: 'facebook_direct',
          text: language === 'de' ? "Direkt über Facebook kontaktieren" :
                language === 'es' ? "Contactar directamente por Facebook" :
                "Contact directly via Facebook",
          icon: <Facebook className="h-4 w-4" />,
          action: async () => {
            trackChatbotChoice('facebook_direct', 'Facebook Direct');
            const userTexts = {
              en: "Contact directly via Facebook",
              de: "Direkt über Facebook kontaktieren",
              es: "Contactar directamente por Facebook"
            };
            addUserMessage(userTexts[language as keyof typeof userTexts] || userTexts.en);
            // Direct Facebook profile link
            window.open('https://www.facebook.com/profile.php?id=61578812701701', '_blank');

            setTimeout(() => {
              const botResponses = {
                en: "Facebook Messenger opened for you! Send us a message and we'll get back to you soon. Thanks! 🙏",
                de: "Facebook Messenger wurde für dich geöffnet! Schick uns eine Nachricht und wir melden uns bald. Danke! 🙏",
                es: "¡Facebook Messenger abierto para ti! Envíanos un mensaje y te responderemos pronto. ¡Gracias! 🙏"
              };
              addBotMessage(botResponses[language as keyof typeof botResponses] || botResponses.en);
              setChatStep('complete');
            }, 1000);
          }
        },
        {
          id: 'leave_contact',
          text: language === 'de' ? "Meine Kontaktdaten hinterlassen" :
                language === 'es' ? "Dejar mi información de contacto" :
                "Leave my contact info",
          icon: <UserPlus className="h-4 w-4" />,
          action: () => {
            trackChatbotChoice('leave_contact', 'Leave Contact');
            const userTexts = {
              en: "Leave my contact info",
              de: "Meine Kontaktdaten hinterlassen",
              es: "Dejar mi información de contacto"
            };
            addUserMessage(userTexts[language as keyof typeof userTexts] || userTexts.en);
            showContactMethodOptions();
          }
        }
      ];

      addBotMessage(responses[language as keyof typeof responses] || responses.en, options);
    }
  };

  const showContactMethodOptions = () => {
    const isChineseUser = language === 'zh';

    // Set up available contact methods based on language
    const contactSteps: ContactStep[] = isChineseUser
      ? [
          { type: 'phone', label: '手机号 (WhatsApp)', placeholder: '输入你的手机号...' },
          { type: 'wechat', label: '微信号', placeholder: '输入你的微信号...' }
        ]
      : [
          { type: 'phone', label: language === 'de' ? 'Telefonnummer (WhatsApp)' :
                                  language === 'es' ? 'Número de teléfono (WhatsApp)' :
                                  'Phone number (WhatsApp)',
            placeholder: language === 'de' ? 'Telefonnummer eingeben...' :
                        language === 'es' ? 'Ingresa número de teléfono...' :
                        'Enter phone number...' },
          { type: 'facebook', label: language === 'de' ? 'Facebook-Konto' :
                                   language === 'es' ? 'Cuenta de Facebook' :
                                   'Facebook account',
            placeholder: language === 'de' ? 'Facebook-Konto eingeben...' :
                        language === 'es' ? 'Ingresa cuenta de Facebook...' :
                        'Enter Facebook account...' }
        ];

    setAvailableContactSteps(contactSteps);
    setChatStep('contact_options');

    const responses = isChineseUser ? {
      zh: "我们支持WhatsApp和微信。你想先分享哪种联系方式？"
    } : {
      en: "We support WhatsApp and Facebook. Which contact method would you like to share first?",
      de: "Wir unterstützen WhatsApp und Facebook. Welche Kontaktmethode möchtest du zuerst teilen?",
      es: "Apoyamos WhatsApp y Facebook. ¿Qué método de contacto te gustaría compartir primero?"
    };

    const options: ChatOption[] = contactSteps.map((step) => ({
      id: `contact_${step.type}`,
      text: step.label,
      icon: step.type === 'phone' ? <Phone className="h-4 w-4" /> :
            step.type === 'facebook' ? <Facebook className="h-4 w-4" /> :
            step.type === 'wechat' ? <MessageCircle className="h-4 w-4" /> : undefined,
      action: () => {
        trackChatbotChoice(`contact_method_${step.type}`, `Contact Method: ${step.type}`);
        addUserMessage(step.label);
        setCurrentContactStep(step);
        setChatStep('contact');
        setTimeout(() => {
          let prompts: Record<string, string>;
          if (step.type === 'phone') {
            prompts = {
              en: `Please enter your ${step.label.toLowerCase()} (e.g., +49 176 12345678):`,
              de: `Bitte gib deine ${step.label.toLowerCase()} ein (z.B. +49 176 12345678):`,
              zh: `请输入你的${step.label}（例如：+49 176 12345678）：`,
              es: `Por favor ingresa tu ${step.label.toLowerCase()} (ej. +49 176 12345678):`
            };
          } else {
            prompts = {
              en: `Please enter your ${step.label.toLowerCase()}:`,
              de: `Bitte gib deine ${step.label.toLowerCase()} ein:`,
              zh: `请输入你的${step.label}：`,
              es: `Por favor ingresa tu ${step.label.toLowerCase()}:`
            };
          }
          addBotMessage(prompts[language as keyof typeof prompts] || prompts.en);
        }, 500);
      }
    }));

    addBotMessage(responses[language as keyof typeof responses] || responses.en, options);
  };

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digit characters except + at the beginning
    const cleaned = value.replace(/[^\d+]/g, '');

    // If it starts with +49, format more flexibly
    if (cleaned.startsWith('+49')) {
      const digits = cleaned.slice(3);
      if (digits.length <= 2) {
        return `+49 ${digits}`;
      } else if (digits.length <= 5) {
        return `+49 ${digits.slice(0, 2)} ${digits.slice(2)}`;
      } else if (digits.length <= 8) {
        return `+49 ${digits.slice(0, 3)} ${digits.slice(3)}`;
      } else if (digits.length <= 11) {
        return `+49 ${digits.slice(0, 4)} ${digits.slice(4)}`;
      } else {
        return `+49 ${digits.slice(0, 5)} ${digits.slice(5, 17)}`;
      }
    }

    // If it starts with 0, assume German number and convert to +49
    if (cleaned.startsWith('0')) {
      const digits = cleaned.slice(1);
      if (digits.length <= 2) {
        return `+49 ${digits}`;
      } else if (digits.length <= 5) {
        return `+49 ${digits.slice(0, 2)} ${digits.slice(2)}`;
      } else if (digits.length <= 8) {
        return `+49 ${digits.slice(0, 3)} ${digits.slice(3)}`;
      } else if (digits.length <= 11) {
        return `+49 ${digits.slice(0, 4)} ${digits.slice(4)}`;
      } else {
        return `+49 ${digits.slice(0, 5)} ${digits.slice(5, 17)}`;
      }
    }

    // For other formats, just clean and return
    return cleaned;
  };

  const handleContactSubmit = async () => {
    if (!currentInput.trim() || !currentContactStep) return;

    let contactValue = currentInput.trim();

    // Format phone number if it's a phone input
    if (currentContactStep.type === 'phone') {
      contactValue = formatPhoneNumber(contactValue);
    }

    // Track contact submission
    trackChatbotChoice('contact_submitted', `Contact Submitted (${currentContactStep.type})`);

    addUserMessage(contactValue);

    // Update contact info based on the current step type
    const updatedContactInfo = {
      ...contactInfo,
      [currentContactStep.type === 'phone' ? 'phone' :
       currentContactStep.type === 'facebook' ? 'facebook' : 'wechat']: contactValue
    };

    setContactInfo(updatedContactInfo);

    setCurrentInput('');

    console.log('Updated contact info:', updatedContactInfo);

    // Remove the current step from available steps
    const remainingSteps = availableContactSteps.filter(step => step.type !== currentContactStep.type);
    setAvailableContactSteps(remainingSteps);
    setCurrentContactStep(null);

    setTimeout(() => {
      if (remainingSteps.length > 0) {
        // Ask if they want to provide another contact method
        const moreContactTexts = {
          en: `Great! I've got your ${currentContactStep.label.toLowerCase()}: ${contactValue}. Would you like to share another contact method?`,
          de: `Super! Ich habe deine ${currentContactStep.label.toLowerCase()}: ${contactValue}. Möchtest du eine weitere Kontaktmethode teilen?`,
          zh: `太好了！我已经记下了你的${currentContactStep.label}：${contactValue}。你想再分享另一种联系方式吗？`,
          es: `¡Genial! Tengo tu ${currentContactStep.label.toLowerCase()}: ${contactValue}. ¿Te gustaría compartir otro método de contacto?`
        };

        const options: ChatOption[] = [
          {
            id: 'add_more_contact',
            text: language === 'de' ? "Ja, weitere hinzufügen" :
                  language === 'es' ? "Sí, agregar más" :
                  language === 'zh' ? "是的，再添加一个" :
                  "Yes, add more",
            action: () => {
              trackChatbotChoice('add_more_contact', 'Add More Contact');
              addUserMessage(language === 'de' ? "Ja, weitere hinzufügen" :
                            language === 'es' ? "Sí, agregar más" :
                            language === 'zh' ? "是的，再添加一个" :
                            "Yes, add more");
              showRemainingContactOptions();
            }
          },
          {
            id: 'finish_contact',
            text: language === 'de' ? "Nein, das reicht" :
                  language === 'es' ? "No, eso es suficiente" :
                  language === 'zh' ? "不用了，这就够了" :
                  "No, that's enough",
            action: () => {
              trackChatbotChoice('finish_contact', 'Finish Contact');
              addUserMessage(language === 'de' ? "Nein, das reicht" :
                            language === 'es' ? "No, eso es suficiente" :
                            language === 'zh' ? "不用了，这就够了" :
                            "No, that's enough");
              finishContactCollection(updatedContactInfo);
            }
          }
        ];

        addBotMessage(moreContactTexts[language as keyof typeof moreContactTexts] || moreContactTexts.en, options);
      } else {
        // No more contact methods available, finish
        finishContactCollection(updatedContactInfo);
      }
    }, 500);
  };

  const showRemainingContactOptions = () => {
    setChatStep('contact_options');

    const isChineseUser = language === 'zh';
    const responses = isChineseUser ? {
      zh: "你想分享哪种其他联系方式？"
    } : {
      en: "Which other contact method would you like to share?",
      de: "Welche andere Kontaktmethode möchtest du teilen?",
      es: "¿Qué otro método de contacto te gustaría compartir?"
    };

    const options: ChatOption[] = availableContactSteps.map((step) => ({
      id: `contact_${step.type}`,
      text: step.label,
      icon: step.type === 'phone' ? <Phone className="h-4 w-4" /> :
            step.type === 'facebook' ? <Facebook className="h-4 w-4" /> :
            step.type === 'wechat' ? <MessageCircle className="h-4 w-4" /> : undefined,
      action: () => {
        trackChatbotChoice(`contact_method_${step.type}`, `Contact Method: ${step.type}`);
        addUserMessage(step.label);
        setCurrentContactStep(step);
        setChatStep('contact');
        setTimeout(() => {
          let prompts: Record<string, string>;
          if (step.type === 'phone') {
            prompts = {
              en: `Please enter your ${step.label.toLowerCase()} (e.g., +49 176 12345678):`,
              de: `Bitte gib deine ${step.label.toLowerCase()} ein (z.B. +49 176 12345678):`,
              zh: `请输入你的${step.label}（例如：+49 176 12345678）：`,
              es: `Por favor ingresa tu ${step.label.toLowerCase()} (ej. +49 176 12345678):`
            };
          } else {
            prompts = {
              en: `Please enter your ${step.label.toLowerCase()}:`,
              de: `Bitte gib deine ${step.label.toLowerCase()} ein:`,
              zh: `请输入你的${step.label}：`,
              es: `Por favor ingresa tu ${step.label.toLowerCase()}:`
            };
          }
          addBotMessage(prompts[language as keyof typeof prompts] || prompts.en);
        }, 500);
      }
    }));

    addBotMessage(responses[language as keyof typeof responses] || responses.en, options);
  };

  const finishContactCollection = (finalContactInfo?: any) => {
    setChatStep('confirm');

    // Use the passed contact info or fall back to current state
    const currentContactInfo = finalContactInfo || contactInfo;
    console.log('Finishing contact collection with:', currentContactInfo);

    const contactMethods = [];
    if (currentContactInfo.phone) contactMethods.push(`${language === 'zh' ? '手机号' : 'Phone'}: ${currentContactInfo.phone}`);
    if (currentContactInfo.facebook) contactMethods.push(`Facebook: ${currentContactInfo.facebook}`);
    if (currentContactInfo.wechat) contactMethods.push(`${language === 'zh' ? '微信' : 'WeChat'}: ${currentContactInfo.wechat}`);

    const contactSummary = contactMethods.join(', ');

    const confirmTexts = {
      en: `Perfect! I've got your contact info: ${contactSummary}. We'll reach out to you within one business day. Thanks for your trust! 🙏`,
      de: `Perfekt! Ich habe deine Kontaktdaten: ${contactSummary}. Wir melden uns innerhalb eines Werktages bei dir. Danke für dein Vertrauen! 🙏`,
      zh: `完美！我已经记下了你的联系方式：${contactSummary}。我们会在一个工作日内联系你。感谢你的信任！🙏`,
      es: `¡Perfecto! Tengo tu información de contacto: ${contactSummary}. Te contactaremos dentro de un día hábil. ¡Gracias por tu confianza! 🙏`
    };

    addBotMessage(confirmTexts[language as keyof typeof confirmTexts] || confirmTexts.en);

    // Submit to backend with the final contact info
    setTimeout(() => {
      submitAllContactInfo(currentContactInfo);
    }, 100);
  };



  const submitAllContactInfo = async (finalContactInfo?: any) => {
    setIsSubmitting(true);

    try {
      // Use the passed contact info or fall back to current state
      const currentContactInfo = finalContactInfo || contactInfo;
      console.log('Submitting contact info:', currentContactInfo);

      // Determine the appropriate Status based on contact methods provided
      let status = "WhatsappConnect"; // Default to WhatsappConnect
      if (currentContactInfo.phone) {
        status = "WhatsappConnect";
      } else if (currentContactInfo.facebook) {
        status = "FacebookConnect";
      } else if (currentContactInfo.wechat) {
        status = "WechatConnect";
      }

      const submissionData = {
        FirstName: "",
        LastName: "",
        PhoneNumber: currentContactInfo.phone || '',
        Facebook: currentContactInfo.facebook || '',
        Wechat_ZH: currentContactInfo.wechat || '',
        ConnectWhatsapp: currentContactInfo.phone ? 'yes' : 'no',
        ConnectFacebook: currentContactInfo.facebook ? 'yes' : 'no',
        ConnectWechat_ZH: currentContactInfo.wechat ? 'yes' : 'no',
        Status: status,
        Source: "Chatbot",
        Language: language,
        formType: "worker"
      };

      console.log('Submitting to NocoDB:', submissionData);

      // Use the correct port for Netlify dev server
      const baseUrl = window.location.hostname === 'localhost' ? 'http://localhost:8888' : '';
      const response = await fetch(`${baseUrl}/.netlify/functions/submit-form`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      const result = await response.json();
      console.log('NocoDB submission result:', result);

      setChatStep('complete');
    } catch (error) {
      console.error('Failed to submit contact info:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderMessage = (message: ChatMessage) => (
    <div key={message.id} className={`flex ${message.isBot ? 'justify-start' : 'justify-end'} mb-4`}>
      <div className={`max-w-[80%] p-3 rounded-lg ${
        message.isBot
          ? 'bg-muted text-foreground'
          : 'bg-primary text-primary-foreground'
      }`}>
        <p className="text-sm whitespace-pre-wrap">{message.text}</p>
        {message.options && (
          <div className="mt-3 space-y-2">
            {message.options.map((option) => (
              <Button
                key={option.id}
                variant="outline"
                size="sm"
                onClick={option.action}
                className="w-full justify-start text-left h-auto py-2 px-3 whitespace-normal break-words"
              >
                {option.icon && <span className="mr-2">{option.icon}</span>}
                {option.text}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="fixed bottom-4 right-0 sm:bottom-6 sm:right-6 z-40">
      {!isOpen ? (
        <Button
          onClick={handleOpenChat}
          size="lg"
          className="h-12 sm:h-14 px-4 sm:px-6 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 active:scale-95 bg-primary hover:bg-primary/90 text-primary-foreground font-semibold text-sm sm:text-base group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
          aria-label="Chat with us"
        >
          <MessageCircle className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 transition-transform group-hover:scale-110" />
          <span className="hidden sm:inline">{t('chatbot.chat_with_us')}</span>
          <span className="sm:hidden">{t('chatbot.chat')}</span>
        </Button>
      ) : (
        <div className="bg-white rounded-lg shadow-2xl w-80 sm:w-96 max-h-[600px] overflow-hidden">
          {/* Header */}
          <div className="bg-primary text-primary-foreground p-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              <h3 className="font-semibold">{t('chatbot.title')}</h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="text-primary-foreground hover:bg-primary-foreground/20 h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Messages */}
          <div className="p-4 max-h-[450px] overflow-y-auto">
            {messages.map(renderMessage)}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          {chatStep === 'contact' && (
            <div className="p-4 bg-muted/30">
              <div className="flex gap-2">
                <Input
                  value={currentInput}
                  onChange={(e) => setCurrentInput(e.target.value)}
                  placeholder={currentContactStep?.placeholder || "Enter contact info..."}
                  onKeyDown={(e) => e.key === 'Enter' && handleContactSubmit()}
                  disabled={isSubmitting}
                />
                <Button
                  onClick={handleContactSubmit}
                  disabled={!currentInput.trim() || isSubmitting}
                  size="sm"
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FloatingChatbot;
