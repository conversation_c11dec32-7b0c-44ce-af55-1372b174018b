import { useState, useEffect, useRef } from "react";
import { FileText } from "lucide-react";
import { useI18n } from "@/contexts/I18nContext";
import { useFormTab, TabType } from "@/contexts/FormTabContext";
import { useTheme } from "@/contexts/ThemeContext";
import WorkerFormSection from "./WorkerFormSection";
import CompanyFormSection from "./CompanyFormSection";

const FormSection = () => {
  const { t } = useI18n();
  const { preferredTab, getDefaultTabForPage, clearPreferredTab } = useFormTab();
  const { currentTheme } = useTheme();
  const [activeTab, setActiveTab] = useState<TabType>("worker");
  const isInitialMount = useRef(true);

  // Set initial tab based on page on first mount
  useEffect(() => {
    if (isInitialMount.current) {
      const defaultTab = getDefaultTabForPage();
      setActiveTab(defaultTab);
      isInitialMount.current = false;
    }
  }, [getDefaultTabForPage]);

  // Handle preferred tab changes (from navigation actions)
  useEffect(() => {
    if (preferredTab && !isInitialMount.current) {
      // Always respect programmatic navigation
      setActiveTab(preferredTab);

      // Clear the preferred tab after applying it
      setTimeout(() => {
        clearPreferredTab();
      }, 100);
    }
  }, [preferredTab, clearPreferredTab]);

  // Determine which form to show based on current theme and active tab
  const showWorkerForm = activeTab === "worker";
  const showCompanyForm = activeTab === "company";

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/30" data-form-section>
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 rounded-full bg-primary flex items-center justify-center">
              <FileText className="h-8 w-8 text-primary-foreground" />
            </div>
          </div>
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
            {t('forms.title')}
          </h2>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* Show only the appropriate form based on active tab - no tab switcher UI */}
          <div className="w-full">
            {showWorkerForm && (
              <div className="relative">
                <WorkerFormSection />
              </div>
            )}

            {showCompanyForm && (
              <div className="relative">
                <CompanyFormSection />
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FormSection;
