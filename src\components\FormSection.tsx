import { useState, useEffect, useRef } from "react";
import { FileText, ChevronDown } from "lucide-react";
import { useI18n } from "@/contexts/I18nContext";
import { useFormTab, TabType } from "@/contexts/FormTabContext";
import { useTheme } from "@/contexts/ThemeContext";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import WorkerFormSection from "./WorkerFormSection";
import CompanyFormSection from "./CompanyFormSection";

const FormSection = () => {
  const { t } = useI18n();
  const { preferredTab, getDefaultTabForPage, clearPreferredTab } = useFormTab();
  const { currentTheme } = useTheme();
  const [activeTab, setActiveTab] = useState<TabType>("worker");
  const [isWorkerFormExpanded, setIsWorkerFormExpanded] = useState(false);
  const [isCompanyFormExpanded, setIsCompanyFormExpanded] = useState(false);
  const isInitialMount = useRef(true);

  // Set initial tab based on page on first mount and expand appropriate form
  useEffect(() => {
    if (isInitialMount.current) {
      const defaultTab = getDefaultTabForPage();
      setActiveTab(defaultTab);

      // Expand the appropriate form based on the default tab
      if (defaultTab === "worker") {
        setIsWorkerFormExpanded(true);
        setIsCompanyFormExpanded(false);
      } else {
        setIsCompanyFormExpanded(true);
        setIsWorkerFormExpanded(false);
      }

      isInitialMount.current = false;
    }
  }, [getDefaultTabForPage]);

  // Handle preferred tab changes (from navigation actions)
  useEffect(() => {
    if (preferredTab && !isInitialMount.current) {
      // Always respect programmatic navigation
      setActiveTab(preferredTab);

      // Expand the appropriate form based on the preferred tab
      if (preferredTab === "worker") {
        setIsWorkerFormExpanded(true);
        setIsCompanyFormExpanded(false);
      } else {
        setIsCompanyFormExpanded(true);
        setIsWorkerFormExpanded(false);
      }

      // Clear the preferred tab after applying it
      setTimeout(() => {
        clearPreferredTab();
      }, 100);
    }
  }, [preferredTab, clearPreferredTab]);

  // Both forms are now always rendered but collapsed by default

  // Handle keyboard navigation for accessibility
  const handleKeyDown = (event: React.KeyboardEvent, formType: 'worker' | 'company') => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (formType === 'worker') {
        setIsWorkerFormExpanded(!isWorkerFormExpanded);
      } else {
        setIsCompanyFormExpanded(!isCompanyFormExpanded);
      }
    }
  };

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/30" data-form-section>
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 rounded-full bg-primary flex items-center justify-center">
              <FileText className="h-8 w-8 text-primary-foreground" />
            </div>
          </div>
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
            {t('forms.title')}
          </h2>
        </div>

        <div className="max-w-4xl mx-auto space-y-6">
          {/* Worker Form Section */}
          <Collapsible
            open={isWorkerFormExpanded}
            onOpenChange={setIsWorkerFormExpanded}
            className="w-full"
          >
            <CollapsibleTrigger
              className="w-full group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg"
              aria-expanded={isWorkerFormExpanded}
              aria-controls="worker-form-content"
              onKeyDown={(e) => handleKeyDown(e, 'worker')}
            >
              <div className="flex items-center justify-between p-6 bg-card border border-border rounded-lg hover:bg-accent/50 transition-colors duration-200 cursor-pointer touch-manipulation min-h-[60px]">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                    <FileText className="h-6 w-6 text-primary" />
                  </div>
                  <div className="text-left">
                    <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors">
                      {t('worker_form.title')}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {t('worker_form.subtitle')}
                    </p>
                  </div>
                </div>
                <ChevronDown
                  className={`h-6 w-6 text-muted-foreground transition-transform duration-200 ${
                    isWorkerFormExpanded ? 'rotate-180' : ''
                  }`}
                  aria-hidden="true"
                />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent
              id="worker-form-content"
              className="overflow-hidden transition-all duration-300 ease-in-out data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
            >
              <div className="pt-4">
                <WorkerFormSection />
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Company Form Section */}
          <Collapsible
            open={isCompanyFormExpanded}
            onOpenChange={setIsCompanyFormExpanded}
            className="w-full"
          >
            <CollapsibleTrigger
              className="w-full group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg"
              aria-expanded={isCompanyFormExpanded}
              aria-controls="company-form-content"
            >
              <div className="flex items-center justify-between p-6 bg-card border border-border rounded-lg hover:bg-accent/50 transition-colors duration-200 cursor-pointer touch-manipulation min-h-[60px]">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                    <FileText className="h-6 w-6 text-primary" />
                  </div>
                  <div className="text-left">
                    <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors">
                      {t('company_form.title')}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {t('company_form.subtitle')}
                    </p>
                  </div>
                </div>
                <ChevronDown
                  className={`h-6 w-6 text-muted-foreground transition-transform duration-200 ${
                    isCompanyFormExpanded ? 'rotate-180' : ''
                  }`}
                  aria-hidden="true"
                />
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent
              id="company-form-content"
              className="overflow-hidden transition-all duration-300 ease-in-out data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
            >
              <div className="pt-4">
                <CompanyFormSection />
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      </div>
    </section>
  );
};

export default FormSection;
