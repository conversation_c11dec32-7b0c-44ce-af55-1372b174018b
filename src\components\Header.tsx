import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useI18n } from '@/contexts/I18nContext';
import { useNavigation } from '@/contexts/NavigationContext';
import { useFormNavigation } from '@/hooks/useFormNavigation';
import { useTheme } from '@/contexts/ThemeContext';
import LanguageSwitcher from './LanguageSwitcher';

interface HeaderProps {
  variant?: 'job-seeker' | 'employer' | 'blog';
  showGetStarted?: boolean;
}

const Header: React.FC<HeaderProps> = ({
  variant = 'job-seeker',
  showGetStarted = false
}) => {
  const { t } = useI18n();
  const navigate = useNavigate();
  const { markInternalNavigation } = useNavigation();
  const { scrollToFormSection } = useFormNavigation();
  const { currentTheme } = useTheme();

  // Determine audience based on current theme
  const audience = currentTheme === 'employer' ? 'Employer' : 'Job-seeker';

  const handleToggleClick = () => {
    markInternalNavigation('header-toggle');
    if (variant === 'employer') {
      navigate('/');
    } else {
      navigate('/hiring');
    }
  };

  const handleBlogClick = () => {
    navigate(`/blog?audience=${encodeURIComponent(audience)}`);
  };

  const handleLogoClick = () => {
    navigate('/');
  };

  // Determine logo based on variant
  const logoSrc = variant === 'employer' ? '/assets/logos/logo-dark.png' : '/assets/logos/logo-main.png';
  
  // Determine toggle button text
  const toggleText = variant === 'employer' 
    ? t('navigation.toggle_job_seeking')
    : t('navigation.toggle_hiring');
  const toggleTextShort = variant === 'employer'
    ? t('navigation.toggle_job_seeking_short')
    : t('navigation.toggle_hiring_short');

  return (
    <header className="fixed top-0 left-0 right-0 z-[200] backdrop-blur-md bg-primary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <button
              onClick={handleLogoClick}
              className="flex items-center transition-smooth hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2 rounded-lg p-1"
              aria-label="Go to homepage"
            >
              <img
                src={logoSrc}
                alt="Sohnus - AI-powered job platform"
                className="h-8 md:h-10 w-auto"
              />
            </button>
          </div>

          {/* Navigation */}
          <div className="flex items-center gap-2 sm:gap-4">
            {/* Blog Button */}
            <Button
              size="sm"
              variant="outline"
              onClick={handleBlogClick}
              aria-label="Go to blog"
            >
              {t('navigation.blog')}
            </Button>

            {/* Toggle Button - only show if not on blog variant */}
            {variant !== 'blog' && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleToggleClick}
                aria-label={variant === 'employer' ? "Switch to job seeking page" : "Switch to hiring page"}
              >
                <span className="hidden sm:inline">{toggleText}</span>
                <span className="sm:hidden">{toggleTextShort}</span>
              </Button>
            )}

            {/* Language Selector */}
            <LanguageSwitcher />

            {/* Get Started Button - only show if enabled */}
            {showGetStarted && (
              <Button
                size="sm"
                variant="secondary"
                aria-label="Get started with Sohnus"
                className="text-xs sm:text-sm px-2 sm:px-3 md:px-4"
                onClick={() => {
                  scrollToFormSection();
                }}
              >
                <span className="hidden sm:inline">{t('forms.title')}</span>
                <span className="sm:hidden">Start</span>
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
