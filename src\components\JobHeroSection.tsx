import { But<PERSON> } from "@/components/ui/button";
import { Star } from "lucide-react";
import { useI18n } from "@/contexts/I18nContext";
import { useNavigate } from "react-router-dom";
import { useNavigation } from "@/contexts/NavigationContext";
import { useFormNavigation } from "@/hooks/useFormNavigation";
import LanguageSwitcher from "./LanguageSwitcher";

const JobHeader = () => {
  const { t } = useI18n();
  const navigate = useNavigate();
  const { markInternalNavigation } = useNavigation();
  const { scrollToFormSection } = useFormNavigation();

  const handleToggleClick = () => {
    // Mark this as internal navigation to prevent modal
    markInternalNavigation('header-toggle');
    navigate('/hiring');
  };

  return (
    <header className={`fixed top-0 left-0 right-0 z-[200] backdrop-blur-md bg-primary`} style={{ position: 'fixed', transform: 'none' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <button
              onClick={() => window.location.href = '/'}
              className="flex items-center transition-smooth hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2 rounded-lg p-1"
              aria-label="Go to homepage"
            >
              <img
                src="/assets/logos/logo-main.png"
                alt="Sohnus - AI-powered job platform connecting workers and employers in Germany"
                className="h-8 md:h-10 w-auto"
              />
            </button>
          </div>

          {/* Navigation */}
          <div className="flex items-center gap-2 sm:gap-4">
            {/* Blog Button */}
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                navigate('/blog?audience=Job-seeker');
              }}
              aria-label="Go to blog"
            >
              {t('navigation.blog')}
            </Button>

            {/* Toggle Button */}
            <Button
              size="sm"
              variant="outline"
              onClick={handleToggleClick}
              aria-label="Switch to hiring page"
            >
              <span className="hidden sm:inline">{t('navigation.toggle_hiring')}</span>
              <span className="sm:hidden">{t('navigation.toggle_hiring_short')}</span>
            </Button>

            {/* Language Selector */}
            <LanguageSwitcher />

            {/* Get Started Button */}
            <Button
              size="sm"
              variant="secondary"
              aria-label="Get started with Sohnus"
              className="text-xs sm:text-sm px-2 sm:px-3 md:px-4"
              onClick={() => {
                // Use smart navigation that automatically selects the right tab
                scrollToFormSection();
              }}
            >
              <span className="hidden sm:inline">{t('forms.title')}</span>
              <span className="sm:hidden">Start</span>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

const JobHeroSection = () => {
  const { t } = useI18n();
  const { scrollToFormSection } = useFormNavigation();

  return (
    <section className="relative overflow-hidden bg-gradient-hero w-full">
      <JobHeader />
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-0 min-h-[700px]">
          {/* Content */}
          <div className="pt-24 pb-20 px-4 sm:px-6 lg:px-8 space-y-8 flex flex-col justify-center">
            <div className="space-y-4">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-secondary leading-tight">
                {t('hero.title')}
                <br />
                <span className="text-secondary/90">{t('hero.title_powered')}</span>
              </h1>
              <p className="text-lg sm:text-xl text-primary-foreground max-w-2xl">
                {t('hero.subtitle')}
                <br />
                {t('hero.subtitle_2')}
              </p>
            </div>

            <div className="flex flex-wrap gap-4">
              <Button
                size="lg"
                variant="secondary"
                onClick={() => {
                  // Use smart navigation that automatically selects the right tab
                  scrollToFormSection();
                }}
              >
                {t('hero.cta_worker')}
              </Button>
            </div>

            {/* Trust indicators */}
            <div className="flex items-center gap-6 pt-4">
              <div className="flex items-center gap-2">
                <div className="flex -space-x-1">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <Star key={i} className="h-5 w-5 fill-secondary text-secondary" />
                  ))}
                </div>
                <span className="text-secondary/80 font-medium">{t('hero.trust_indicator')}</span>
              </div>
            </div>
          </div>

          {/* Hero Image with floating elements */}
          <div className="relative flex items-end">
            <div className="relative w-full">
              <img
                src="/assets/images/job/header.png"
                alt="Professional worker receiving AI-powered job matching notifications showing 2,500+ interviews and 98% success rate"
                className="ml-auto h-auto max-w-[600px] block object-bottom"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default JobHeroSection;
