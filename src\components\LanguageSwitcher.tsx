import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import { useI18n } from '@/contexts/I18nContext';

interface Language {
  code: 'en' | 'de' | 'zh' | 'es';
  name: string;
  flag: string;
}

import { get } from 'country-flag-emoji';

const languages: Language[] = [
  {
    code: 'en',
    name: 'English',
    flag: get('US')?.emoji || <img src="\public\assets\images\flags\us.png" alt="US" className="inline h-4" />
  },
  {
    code: 'de',
    name: 'Deuts<PERSON>',
    flag: get('DE')?.emoji || <img src="\public\assets\images\flags\de.png" alt="DE" className="inline h-4" />
  },
  {
    code: 'zh',
    name: '中文',
    flag: get('CN')?.emoji || <img src="\public\assets\images\flags\cn.png" alt="CN" className="inline h-4" />
  },
  {
    code: 'es',
    name: 'Espa<PERSON><PERSON>',
    flag: get('ES')?.emoji || <img src="\public\assets\images\flags\es.png" alt="ES" className="inline h-4" />
  }
];


const LanguageSwitcher: React.FC = () => {
  const { language, setLanguage } = useI18n();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLButtonElement>(null);

  const currentLanguage = languages.find(lang => lang.code === language);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Close dropdown on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
        triggerRef.current?.focus();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen]);

  const handleLanguageSelect = (langCode: 'en' | 'de' | 'zh' | 'es') => {
    setLanguage(langCode);
    setIsOpen(false);
    triggerRef.current?.focus();
  };

  const handleKeyDown = (event: React.KeyboardEvent, langCode?: 'en' | 'de' | 'zh' | 'es') => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (langCode) {
        handleLanguageSelect(langCode);
      } else {
        setIsOpen(!isOpen);
      }
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        ref={triggerRef}
        onClick={() => setIsOpen(!isOpen)}
        onKeyDown={(e) => handleKeyDown(e)}
        className="flex items-center gap-2 px-3 py-2 bg-white/90 hover:bg-white border border-gray-300 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-label="Select language"
      >
        {/* Current Language Flag */}
        <span
          className="text-base leading-none"
          role="img"
          aria-label={`${currentLanguage?.name} flag`}
        >
          {currentLanguage?.flag}
        </span>

        {/* Current Language Name */}
        <span className="hidden sm:inline truncate max-w-[80px]">
          {currentLanguage?.name}
        </span>

        {/* Dropdown Arrow */}
        <ChevronDown
          className={`h-4 w-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          aria-hidden="true"
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-[300] py-1">
          <div role="listbox" aria-label="Language options">
            {languages.map((lang) => (
              <button
                key={lang.code}
                onClick={() => handleLanguageSelect(lang.code)}
                onKeyDown={(e) => handleKeyDown(e, lang.code)}
                className="w-full flex items-center gap-3 px-3 py-2 text-sm hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors"
                role="option"
                aria-selected={lang.code === language}
                tabIndex={0}
              >
                {/* Flag Emoji */}
                <span
                  className="text-base leading-none flex-shrink-0"
                  role="img"
                  aria-label={`${lang.name} flag`}
                >
                  {lang.flag}
                </span>

                {/* Language Name */}
                <span className="flex-1 text-left truncate">
                  {lang.name}
                </span>

                {/* Check Icon for Selected Language */}
                {lang.code === language && (
                  <Check
                    className="h-4 w-4 text-green-600 flex-shrink-0"
                    aria-hidden="true"
                  />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageSwitcher;