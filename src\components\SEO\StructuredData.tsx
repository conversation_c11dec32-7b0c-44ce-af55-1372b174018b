import { useI18n } from '@/contexts/I18nContext';
import { useLocation } from 'react-router-dom';

export const useStructuredData = () => {
  const { language } = useI18n();
  const location = useLocation();
  const baseUrl = 'https://sohnus.com'; // Replace with actual domain

  const getOrganizationSchema = () => ({
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Sohnus",
    "url": baseUrl,
    "logo": `${baseUrl}/logo.png`,
    "description": language === 'de' 
      ? "KI-gestützte Jobplattform für Arbeitnehmer und Recruiting-Plattform für Arbeitgeber in Deutschland"
      : language === 'zh'
      ? "面向德国求职者和雇主的AI求职和招聘平台"
      : "AI-powered job platform for workers and recruiting platform for employers in Germany",
    "foundingDate": "2024",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["German", "English", "Chinese"]
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "DE"
    },
    "sameAs": [
      // Add social media URLs when available
    ]
  });

  const getJobBoardSchema = () => ({
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Sohnus",
    "url": baseUrl,
    "description": language === 'de'
      ? "Finden Sie Ihren Traumjob in Deutschland mit unserer KI-gestützten Jobplattform"
      : language === 'zh'
      ? "使用我们的AI求职平台在德国找到您的理想工作"
      : "Find your dream job in Germany with our AI-powered job platform",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Sohnus",
      "logo": `${baseUrl}/logo.png`
    }
  });

  const getServiceSchema = () => {
    const isHiringPage = location.pathname === '/hiring';
    
    return {
      "@context": "https://schema.org",
      "@type": "Service",
      "name": isHiringPage 
        ? (language === 'de' ? "KI-Recruiting-Service" : language === 'zh' ? "AI招聘服务" : "AI Recruiting Service")
        : (language === 'de' ? "KI-Jobvermittlung" : language === 'zh' ? "AI求职服务" : "AI Job Matching Service"),
      "description": isHiringPage
        ? (language === 'de' 
          ? "Intelligente Personalsuche und Recruiting-Lösungen für deutsche Unternehmen"
          : language === 'zh'
          ? "为德国公司提供智能人才招聘和招聘解决方案"
          : "Intelligent talent acquisition and recruiting solutions for German companies")
        : (language === 'de'
          ? "KI-gestützte Jobsuche und Karriereberatung für Arbeitnehmer in Deutschland"
          : language === 'zh'
          ? "为德国求职者提供AI求职和职业咨询服务"
          : "AI-powered job search and career guidance for workers in Germany"),
      "provider": {
        "@type": "Organization",
        "name": "Sohnus",
        "url": baseUrl
      },
      "areaServed": {
        "@type": "Country",
        "name": "Germany"
      },
      "serviceType": isHiringPage ? "Recruiting" : "Job Placement",
      "category": isHiringPage ? "Human Resources" : "Employment Services"
    };
  };

  const getBreadcrumbSchema = () => {
    const items = [
      {
        "@type": "ListItem",
        "position": 1,
        "name": language === 'de' ? "Startseite" : language === 'zh' ? "首页" : "Home",
        "item": baseUrl
      }
    ];

    if (location.pathname === '/hiring') {
      items.push({
        "@type": "ListItem",
        "position": 2,
        "name": language === 'de' ? "Für Arbeitgeber" : language === 'zh' ? "雇主" : "For Employers",
        "item": `${baseUrl}/hiring`
      });
    }

    return {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": items
    };
  };

  const getFAQSchema = () => ({
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": language === 'de' 
          ? "Wie funktioniert die KI-gestützte Jobsuche?"
          : language === 'zh'
          ? "AI求职是如何工作的？"
          : "How does AI-powered job search work?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": language === 'de'
            ? "Unsere KI analysiert Ihre Fähigkeiten und Präferenzen, um Sie mit den besten verfügbaren Stellenangeboten in Deutschland zu verbinden."
            : language === 'zh'
            ? "我们的AI分析您的技能和偏好，将您与德国最佳的工作机会匹配。"
            : "Our AI analyzes your skills and preferences to match you with the best available job opportunities in Germany."
        }
      },
      {
        "@type": "Question",
        "name": language === 'de'
          ? "Ist die Nutzung der Plattform kostenlos?"
          : language === 'zh'
          ? "使用平台是免费的吗？"
          : "Is the platform free to use?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": language === 'de'
            ? "Ja, die Jobsuche auf unserer Plattform ist für Arbeitnehmer völlig kostenlos."
            : language === 'zh'
            ? "是的，我们平台上的求职服务对求职者完全免费。"
            : "Yes, job searching on our platform is completely free for workers."
        }
      }
    ]
  });

  return {
    getOrganizationSchema,
    getJobBoardSchema,
    getServiceSchema,
    getBreadcrumbSchema,
    getFAQSchema
  };
};
