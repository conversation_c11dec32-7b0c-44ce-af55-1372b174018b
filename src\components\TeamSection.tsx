import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Linkedin, Twitter, Mail } from "lucide-react";
import { useI18n } from "@/contexts/I18nContext";
import teamPhoto from "@/assets/team-photo.jpg";

const TeamSection = () => {
  const { t } = useI18n();
  const teamMembers = [
    {
      key: "roger",
      image: "/assets/images/team/Roger.jpg",
      social: {
        linkedin: "https://www.linkedin.com/in/rogerioborgesferreira/",
        email: "<EMAIL>"
      }
    },
    {
      key: "bijun",
      image: "/assets/images/team/Bijun.jpg",
      social: {
        linkedin: "https://www.linkedin.com/in/bijun-li-41bb7188/",
        email: "<EMAIL>"
      }
    },
    {
      key: "yuanwei",
      image: "/assets/images/team/Yuanwei.jpg",
      social: {
        linkedin: "https://www.linkedin.com/in/yuanweifang/",
        email: "<EMAIL>"
      }
    },
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-background">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-heading font-bold text-foreground mb-4">
            {t('team.title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            {t('team.subtitle')}
          </p>
        </div>

        {/* Team Hero Image */}
        <div className="mb-16">
          <div className="relative rounded-2xl overflow-hidden shadow-card">
            <img 
              src={teamPhoto} 
              alt="Sohnus team collaboration" 
              className="w-full h-64 sm:h-80 object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-secondary/80 to-transparent flex items-end">
              <div className="p-8 text-white">
                <h3 className="text-2xl font-bold mb-2">{t('team.hero_title')}</h3>
                <p className="text-white/90">
                  {t('team.hero_subtitle')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Team Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <Card key={index} className="group hover:shadow-card transition-all duration-300 h-full">
              <CardContent className="p-6 h-full flex flex-col">
                <div className="text-center flex-1">
                  <div className="relative mb-4">
                    <img
                      src={member.image}
                      alt={t(`team.members.${member.key}.name`)}
                      className="w-24 h-24 rounded-full mx-auto object-cover shadow-soft transition-transform duration-300"
                    />
                  </div>
                  <h3 className="text-xl font-semibold text-foreground mb-1">
                    {t(`team.members.${member.key}.name`)}
                  </h3>
                  <p className="text-dark font-small mb-3">
                    {t(`team.members.${member.key}.role`)}
                  </p>
                  <p className="text-sm text-muted-foreground leading-relaxed mb-6 flex-1">
                    {t(`team.members.${member.key}.bio`)}
                  </p>

                  {/* Social Links */}
                  <div className="flex justify-center gap-3 mt-auto">
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 hover:bg-primary hover:text-primary-foreground"
                      asChild
                    >
                      <a href={member.social.linkedin} target="_blank" rel="noopener noreferrer">
                        <Linkedin className="h-4 w-4" />
                      </a>
                    </Button>
                    {/* <Button 
                      variant="ghost" 
                      size="icon"
                      className="h-8 w-8 hover:bg-primary hover:text-primary-foreground"
                      asChild
                    >
                      <a href={member.social.twitter} target="_blank" rel="noopener noreferrer">
                        <Twitter className="h-4 w-4" />
                      </a>
                    </Button> */}
                    <Button 
                      variant="ghost" 
                      size="icon"
                      className="h-8 w-8 hover:bg-primary hover:text-primary-foreground"
                      asChild
                    >
                      <a href={`mailto:${member.social.email}`}>
                        <Mail className="h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        {/* <div className="text-center mt-16">
          <Card className="bg-gradient-hero p-8 shadow-card">
            <CardContent className="p-0">
              <h3 className="text-2xl font-bold text-secondary mb-4">
                Want to Join Our Mission?
              </h3>
              <p className="text-secondary/80 mb-6 max-w-2xl mx-auto">
                We're always looking for passionate individuals who want to help shape the future of work.
                Check out our open positions and become part of the Sohnus family.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button variant="secondary" size="lg">
                  View Open Positions
                </Button>
                <Button variant="outline" size="lg" className="border-secondary/20 text-secondary hover:bg-secondary/10">
                  Learn About Our Culture
                </Button>
              </div>
            </CardContent>
          </Card>
        </div> */}
      </div>
    </section>
  );
};

export default TeamSection;