import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useNavigation } from '@/contexts/NavigationContext';
import { useI18n } from '@/contexts/I18nContext';
import { X } from 'lucide-react';

interface WelcomeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const WelcomeModal: React.FC<WelcomeModalProps> = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const { markInternalNavigation } = useNavigation();
  const { t } = useI18n();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Small delay for smooth entrance animation
      const timer = setTimeout(() => setIsVisible(true), 50);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';

      return () => {
        clearTimeout(timer);
        document.body.style.overflow = 'unset';
      };
    } else {
      setIsVisible(false);
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleJobSeekerClick = () => {
    onClose();
  };

  const handleHiringClick = () => {
    // Mark this as internal navigation to prevent modal on hiring page
    markInternalNavigation('modal');
    onClose();
    navigate('/hiring');
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  // Focus trap for accessibility
  useEffect(() => {
    if (isOpen) {
      const focusableElements = document.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

      const handleTabKey = (e: KeyboardEvent) => {
        if (e.key === 'Tab') {
          if (e.shiftKey) {
            if (document.activeElement === firstElement) {
              lastElement?.focus();
              e.preventDefault();
            }
          } else {
            if (document.activeElement === lastElement) {
              firstElement?.focus();
              e.preventDefault();
            }
          }
        }
      };

      document.addEventListener('keydown', handleTabKey);
      return () => document.removeEventListener('keydown', handleTabKey);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-all duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
      onClick={handleBackdropClick}
      onKeyDown={handleKeyDown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" />
      
      {/* Modal Content */}
      <div
        className={`relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-auto transform transition-all duration-300 ${
          isVisible ? 'scale-100 translate-y-0' : 'scale-95 translate-y-4'
        }`}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 transition-colors z-10"
          aria-label={t('welcome_modal.close_button')}
        >
          <X className="w-5 h-5 text-gray-500" />
        </button>

        {/* Modal Image with text overlay */}
        <div className="relative overflow-hidden rounded-t-2xl">
          <img
            src="/assets/images/welcome.png"
            alt="Sohnus AI-powered job platform welcome screen with friendly hr and staff"
            className="w-full h-80 object-cover"
            loading="eager"
          />

          {/* Text overlay inside image area only */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col items-center justify-end px-6 pb-0

">
          <h2 id="modal-title" className="text-2xl font-bold text-white text-center mb-2">
            {t('welcome_modal.title')}
          </h2>
          <p id="modal-description" className="text-gray-600 mb-6 text-white text-center">
            {t('welcome_modal.description')}
          </p>
          </div>
        </div>

        {/* Modal Content */}
        <div className="p-6 text-center">

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleJobSeekerClick}
             className="w-full bg-brand-yellow text-brand-blue hover:bg-brand-yellow-hover font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-brand-yellow focus:ring-offset-2"
              autoFocus
            >
              {t('welcome_modal.job_seeker_button')}
            </button>

            <button
              onClick={handleHiringClick}
              className="w-full bg-brand-blue text-brand-yellow hover:bg-brand-blue-hover font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-brand-blue focus:ring-offset-2"
            >
              {t('welcome_modal.hiring_button')}
            </button>
          </div>

          {/* Additional Info */}
          <p className="text-xs text-gray-500 mt-4">
            {t('welcome_modal.additional_info')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default WelcomeModal;
