import React, { createContext, useContext, useState, useCallback } from 'react';
import { useLocation } from 'react-router-dom';

export type TabType = 'worker' | 'company';

interface FormTabContextType {
  preferredTab: TabType | null;
  setPreferredTab: (tab: TabType) => void;
  clearPreferredTab: () => void;
  getDefaultTabForPage: () => TabType;
}

const FormTabContext = createContext<FormTabContextType | undefined>(undefined);

export const useFormTab = () => {
  const context = useContext(FormTabContext);
  if (!context) {
    throw new Error('useFormTab must be used within a FormTabProvider');
  }
  return context;
};

interface FormTabProviderProps {
  children: React.ReactNode;
}

export const FormTabProvider: React.FC<FormTabProviderProps> = ({ children }) => {
  const [preferredTab, setPreferredTabState] = useState<TabType | null>(null);
  const location = useLocation();

  const getDefaultTabForPage = useCallback((): TabType => {
    return location.pathname === '/hiring' ? 'company' : 'worker';
  }, [location.pathname]);

  const setPreferredTab = useCallback((tab: TabType) => {
    setPreferredTabState(tab);
  }, []);

  const clearPreferredTab = useCallback(() => {
    setPreferredTabState(null);
  }, []);

  const value: FormTabContextType = {
    preferredTab,
    setPreferredTab,
    clearPreferredTab,
    getDefaultTabForPage,
  };

  return (
    <FormTabContext.Provider value={value}>
      {children}
    </FormTabContext.Provider>
  );
};
