import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'en' | 'de' | 'zh' | 'es';

interface I18nContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  isLoading: boolean;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

interface I18nProviderProps {
  children: ReactNode;
}

export const I18nProvider: React.FC<I18nProviderProps> = ({ children }) => {
  // Detect browser language and map to supported languages
  const detectBrowserLanguage = (): Language => {
    // Get browser language preferences
    const browserLanguages = navigator.languages || [navigator.language];
    console.log('🌐 Browser languages detected:', browserLanguages);

    // Supported language mapping
    const supportedLanguages: Language[] = ['en', 'de', 'zh', 'es'];

    // Check each browser language preference
    for (const browserLang of browserLanguages) {
      const langCode = browserLang.toLowerCase();

      // Direct match (e.g., 'en', 'de', 'zh', 'es')
      if (supportedLanguages.includes(langCode as Language)) {
        console.log(`🎯 Direct language match found: ${langCode}`);
        return langCode as Language;
      }

      // Language family match (e.g., 'en-US' → 'en', 'de-DE' → 'de')
      const langFamily = langCode.split('-')[0];
      if (supportedLanguages.includes(langFamily as Language)) {
        console.log(`🎯 Language family match found: ${langFamily} (from ${langCode})`);
        return langFamily as Language;
      }
    }

    console.log('🔧 No supported language detected, defaulting to English');
    return 'en';
  };

  // Initialize language with auto-detection on first visit
  const getInitialLanguage = (): Language => {
    const savedLanguage = localStorage.getItem('sohnus-language') as Language;

    // If user has previously selected a language, respect their choice
    if (savedLanguage && ['en', 'de', 'zh', 'es'].includes(savedLanguage)) {
      console.log(`🔧 Using saved language preference: ${savedLanguage}`);
      return savedLanguage;
    }

    // First visit - auto-detect browser language
    console.log('🔧 First visit detected, auto-detecting language...');
    const detectedLanguage = detectBrowserLanguage();

    // Save the detected language to localStorage for future visits
    localStorage.setItem('sohnus-language', detectedLanguage);
    console.log(`💾 Saved auto-detected language: ${detectedLanguage}`);

    return detectedLanguage;
  };

  const [language, setLanguage] = useState<Language>(getInitialLanguage);
  const [translations, setTranslations] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(true);

  console.log(`🔄 I18nProvider render - Language: ${language}, Translations loaded: ${Object.keys(translations).length > 0}, Loading: ${isLoading}`);

  // Load translations for the current language
  useEffect(() => {
    const loadTranslations = async () => {
      setIsLoading(true);
      console.log(`🌐 Loading translations for language: ${language}`);
      try {
        const url = `/locales/${language}.json`;
        console.log(`📡 Fetching: ${url}`);
        const response = await fetch(url);
        console.log(`📥 Response:`, response.status, response.statusText, response.ok);

        if (response.ok) {
          const data = await response.json();
          console.log(`✅ Successfully loaded ${language} translations:`, {
            keys: Object.keys(data),
            heroKeys: data.hero ? Object.keys(data.hero) : 'No hero section',
            featuresKeys: data.features ? Object.keys(data.features) : 'No features section'
          });
          setTranslations(data);
        } else {
          console.error(`❌ Failed to load translations for ${language}:`, response.status, response.statusText);
          // Fallback to English if current language fails
          if (language !== 'en') {
            console.log('🔄 Attempting fallback to English...');
            const fallbackResponse = await fetch('/locales/en.json');
            if (fallbackResponse.ok) {
              const fallbackData = await fallbackResponse.json();
              console.log('✅ Fallback English translations loaded');
              setTranslations(fallbackData);
            }
          }
        }
      } catch (error) {
        console.error('💥 Error loading translations:', error);
      } finally {
        console.log(`🏁 Translation loading finished for ${language}`);
        setIsLoading(false);
      }
    };

    // Only load translations if we have a language set
    if (language) {
      loadTranslations();
    }
  }, [language]);

  // Language is now initialized in useState, no need for separate useEffect

  // Save language preference to localStorage
  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('sohnus-language', lang);
  };

  // Translation function with nested key support
  const t = (key: string): string => {
    if (Object.keys(translations).length === 0) {
      // Don't log every single key when translations aren't loaded yet
      return key;
    }

    const keys = key.split('.');
    let value: any = translations;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        console.warn(`Translation key not found: ${key}`);
        return key; // Return the key itself if translation not found
      }
    }

    return typeof value === 'string' ? value : key;
  };

  const contextValue: I18nContextType = {
    language,
    setLanguage: handleSetLanguage,
    t,
    isLoading
  };

  // Don't render children until translations are loaded
  if (isLoading || Object.keys(translations).length === 0) {
    return (
      <I18nContext.Provider value={contextValue}>
        <div className="flex items-center justify-center min-h-screen bg-background">
          <div className="text-lg text-foreground">Loading translations...</div>
        </div>
      </I18nContext.Provider>
    );
  }

  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  );
};

export const useI18n = (): I18nContextType => {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
};

// Helper hook for form options with standardized keys
export const useFormOptions = () => {
  const { t } = useI18n();

  return {
    employmentTypes: [
      { key: 'full_time', label: t('worker_form.options.employment_type.full_time') },
      { key: 'part_time', label: t('worker_form.options.employment_type.part_time') },
      { key: 'contract', label: t('worker_form.options.employment_type.contract') },
      { key: 'temporary', label: t('worker_form.options.employment_type.temporary') },
      { key: 'internship', label: t('worker_form.options.employment_type.internship') }
    ],
    educationLevels: [
      { key: 'no_degree', label: t('worker_form.options.education_level.no_degree') },
      { key: 'vocational_training', label: t('worker_form.options.education_level.vocational_training') },
      { key: 'high_school', label: t('worker_form.options.education_level.high_school') },
      { key: 'bachelors_degree', label: t('worker_form.options.education_level.bachelors_degree') },
      { key: 'masters_degree', label: t('worker_form.options.education_level.masters_degree') },
      { key: 'phd', label: t('worker_form.options.education_level.phd') }
    ],
    germanLevels: [
      { key: 'a1', label: t('worker_form.options.german_level.a1') },
      { key: 'a2', label: t('worker_form.options.german_level.a2') },
      { key: 'b1', label: t('worker_form.options.german_level.b1') },
      { key: 'b2', label: t('worker_form.options.german_level.b2') },
      { key: 'c1', label: t('worker_form.options.german_level.c1') },
      { key: 'c2', label: t('worker_form.options.german_level.c2') },
      { key: 'native_speaker', label: t('worker_form.options.german_level.native_speaker') }
    ],
    drivingLicenses: [
      { key: 'none', label: t('worker_form.options.driving_license.none') },
      { key: 'class_b', label: t('worker_form.options.driving_license.class_b') },
      { key: 'class_c1', label: t('worker_form.options.driving_license.class_c1') },
      { key: 'class_ce', label: t('worker_form.options.driving_license.class_ce') },
      { key: 'forklift_license', label: t('worker_form.options.driving_license.forklift_license') }
    ],
    companyEmploymentModels: [
      { key: 'full_time', label: t('company_form.options.employment_model.full_time') },
      { key: 'part_time', label: t('company_form.options.employment_model.part_time') },
      { key: 'contract', label: t('company_form.options.employment_model.contract') },
      { key: 'temporary', label: t('company_form.options.employment_model.temporary') },
      { key: 'internship', label: t('company_form.options.employment_model.internship') }
    ],
    urgencyLevels: [
      { key: 'immediate', label: t('company_form.options.urgency.immediate') },
      { key: 'urgent', label: t('company_form.options.urgency.urgent') },
      { key: 'normal', label: t('company_form.options.urgency.normal') },
      { key: 'flexible', label: t('company_form.options.urgency.flexible') }
    ]
  };
};
