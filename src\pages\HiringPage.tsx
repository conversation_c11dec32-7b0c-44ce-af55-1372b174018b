import { useState, useEffect } from "react";
import HiringHeroSection from "@/components/HiringHeroSection";
import HiringFeaturesSection from "@/components/HiringFeaturesSection";
import FormSection from "@/components/FormSection";
import LatestBlogPosts from "@/components/LatestBlogPosts";
import TeamSection from "@/components/TeamSection";
import Footer from "@/components/Footer";
import WelcomeModal from "@/components/WelcomeModal";

import SEOHead from "@/components/SEO/SEOHead";
import PerformanceOptimizer from "@/components/SEO/PerformanceOptimizer";
import { useStructuredData } from "@/components/SEO/StructuredData";
import { useFormNavigation } from "@/hooks/useFormNavigation";
import { useNavigation } from "@/contexts/NavigationContext";

const HiringPage = () => {
  const [showModal, setShowModal] = useState(false);
  const { scrollToFormSection } = useFormNavigation();
  const { shouldShowModal } = useNavigation();
  const { getOrganizationSchema, getServiceSchema, getBreadcrumbSchema } = useStructuredData();

  useEffect(() => {
    // Show modal only if user arrived via external navigation
    if (shouldShowModal()) {
      setShowModal(true);
    }
  }, [shouldShowModal]);

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const handleFormSectionClick = () => {
    scrollToFormSection();
  };

  // Structured data for hiring page
  const structuredData = {
    "@context": "https://schema.org",
    "@graph": [
      getOrganizationSchema(),
      getServiceSchema(),
      getBreadcrumbSchema()
    ]
  };

  return (
    <>
      <SEOHead
        structuredData={structuredData}
        alternateLanguages={{
          'en': 'https://sohnus.com/hiring',
          'de': 'https://sohnus.com/hiring',
          'zh': 'https://sohnus.com/hiring'
        }}
      />
      <PerformanceOptimizer />
      <div className="min-h-screen flex flex-col employer-theme">
        <WelcomeModal isOpen={showModal} onClose={handleCloseModal} />
        <div className="flex-1">
          <HiringHeroSection />
          <HiringFeaturesSection />
          <FormSection />
          <LatestBlogPosts />
          <TeamSection />
        </div>
        <Footer />
      </div>
    </>
  );
};

export default HiringPage;
