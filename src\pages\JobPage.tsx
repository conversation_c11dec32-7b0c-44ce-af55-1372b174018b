import { useState, useEffect } from "react";
import JobHeroSection from "@/components/JobHeroSection";
import JobFeaturesSection from "@/components/JobFeaturesSection";
import FormSection from "@/components/FormSection";
import LatestBlogPosts from "@/components/LatestBlogPosts";
import TeamSection from "@/components/TeamSection";
import Footer from "@/components/Footer";
import WelcomeModal from "@/components/WelcomeModal";
import FloatingChatbot from "@/components/FloatingChatbot";
import SEOHead from "@/components/SEO/SEOHead";
import PerformanceOptimizer from "@/components/SEO/PerformanceOptimizer";
import { useStructuredData } from "@/components/SEO/StructuredData";
import { useFormNavigation } from "@/hooks/useFormNavigation";
import { useNavigation } from "@/contexts/NavigationContext";

const JobPage = () => {
  const [showModal, setShowModal] = useState(false);
  const { scrollToFormSection } = useFormNavigation();
  const { shouldShowModal } = useNavigation();
  const { getOrganizationSchema, getJobBoardSchema, getServiceSchema, getBreadcrumbSchema, getFAQSchema } = useStructuredData();

  useEffect(() => {
    // Show modal only if user arrived via external navigation
    if (shouldShowModal()) {
      setShowModal(true);
    }
  }, [shouldShowModal]);

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const handleFormSectionClick = () => {
    scrollToFormSection();
  };

  // Combine structured data schemas for job seekers
  const structuredData = {
    "@context": "https://schema.org",
    "@graph": [
      getOrganizationSchema(),
      getJobBoardSchema(),
      getServiceSchema(),
      getBreadcrumbSchema(),
      getFAQSchema()
    ]
  };

  return (
    <>
      <SEOHead
        structuredData={structuredData}
        alternateLanguages={{
          'en': 'https://sohnus.com/',
          'de': 'https://sohnus.com/',
          'zh': 'https://sohnus.com/'
        }}
      />
      <PerformanceOptimizer />
      <div className="min-h-screen flex flex-col job-seeker-theme">
        <WelcomeModal isOpen={showModal} onClose={handleCloseModal} />
        <div className="flex-1">
          <JobHeroSection />
          <JobFeaturesSection />
          <FormSection />
          <LatestBlogPosts />
          <TeamSection />
        </div>
        <Footer />
        <FloatingChatbot />
      </div>
    </>
  );
};

export default JobPage;
