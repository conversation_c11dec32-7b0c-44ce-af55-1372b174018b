/**
 * Utility functions for modal management
 * Note: Modal now shows on every page load as requested
 */

/**
 * Utility function for form navigation and scrolling
 */
export const scrollToElement = (selector: string, behavior: ScrollBehavior = 'smooth') => {
  const element = document.querySelector(selector);
  if (element) {
    element.scrollIntoView({ behavior, block: 'start', inline: 'nearest' });
  }
};
