{"name": "Data-to-CV", "nodes": [{"parameters": {"httpMethod": "POST", "path": "nocodb-new-row", "responseMode": "lastNode", "options": {}}, "id": "0b3a9f16-f174-4973-ac13-9a1e36c7ce05", "name": "NocoDB Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-3700, -240], "webhookId": "YOUR_WEBHOOK_ID"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "3394c8e7-ff47-49d8-918d-61614ccb19ed", "leftValue": "={{ $json.body.data.resume_pdf_url }}", "operator": {"type": "string", "operation": "isNotEmpty"}}, {"id": "5a37a2ba-821e-4f01-bcc3-9e396ad48304", "leftValue": "={{ $json.UploadedResumeFile }}", "rightValue": "null", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}, {"id": "d51055d7-31d8-4a72-bc8e-3bfbf5c26cba", "leftValue": "={{ $json.Status }}", "rightValue": "", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "id": "098c59eb-200f-4d77-861d-9dec955a2c56", "name": "IF: PDF exists?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2660, 60]}, {"parameters": {"url": "={{ $json.body.data.UploadedResumeFile }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "id": "17c3c5ba-6804-4ed0-9744-6534601ed457", "name": "Download PDF", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-2340, -160]}, {"parameters": {"operation": "pdf", "options": {}}, "id": "0abb355d-0111-4496-8c05-8b344f48723b", "name": "Extract Text from PDF", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-2100, -160]}, {"parameters": {"prompt": "={{ $json.text }}", "messages": {"messageValues": [{"message": "Your task is to extract all necessary data such as first name, last name, experience, known technologies etc. from the provided resume text and return in well-unified JSON format. Do not make things up."}]}}, "id": "975b0c74-293f-4334-9c06-cc04ebb79c4d", "name": "AI解析简历文本", "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.3, "position": [-1860, -160]}, {"parameters": {}, "id": "9f85d18e-45c2-41d9-b3c7-3b66acf0c5a3", "name": "NoOp", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [-2200, 120]}, {"parameters": {"fields": {"values": [{"name": "finalResumeData"}]}, "options": {}}, "id": "c3c0e66c-56e9-432b-955f-8eb657a6e9cb", "name": "Set: 合并数据", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [-1560, -160]}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// 1. 从NocoDB Webhook的输入中提取核心数据\nconst data = $input.item.json;\n\n// 2. (辅助函数) 创建一些帮助函数来翻译和格式化数据\nconst helpers = {\n    createListHtml: (text) => {\n        if (!text) return '';\n        return text.split('\\n').map(line => line.trim().replace(/^- /, '')).filter(line => line).map(item => `<li>${item}</li>`).join('');\n    },\n    translateEducation: (level) => {\n        const map = { 'high_school': 'Hochschulreife / Abitur', 'bachelors_degree': 'Bachelor-Abschluss', 'masters_degree': 'Master-Abschluss', 'doctorate': 'Doktortitel / PhD' };\n        return map[level] || level || 'Keine <PERSON>abe';\n    },\n    translateDrivingLicense: (license) => {\n        const map = { 'class_b': 'Klasse B', 'class_c': 'Klasse C', 'class_d': 'Klasse D' };\n        return map[license] || license || '';\n    },\n    createWorkExperienceHtml: function() {\n        let html = '';\n        for (let i = 1; i <= 3; i++) {\n            const title = data[`Job${i}_Title`];\n            if (!title) continue;\n            const company = data[`Job${i}_Company`], start = data[`Job${i}_Start`], end = data[`Job${i}_End`], duration = data[`Job${i}_Duration`], tasks = data[`Job${i}_Tasks`];\n            let dateRange = '';\n            if (start && end) { dateRange = `${start} - ${end}`; if (duration) { dateRange += ` (${duration})`; } } else if (start) { dateRange = `Ab ${start}`; }\n            html += `<div class=\"timeline-item\"><p class=\"timeline-company\">${company || ''}</p><h3 class=\"timeline-title\">${title}</h3>${dateRange ? `<p class=\"timeline-date\">${dateRange}</p>` : ''}<ul class=\"timeline-tasks\">${this.createListHtml(tasks)}</ul></div>`;\n        }\n        return html;\n    },\n    createEducationHtml: function() {\n        if (data.Education_School) {\n            const level = this.translateEducation(data.EducationLevel), school = data.Education_School, details = data.Education_Detail;\n            let dateRange = '';\n            if (data.Education_Start && data.Education_End) { dateRange = `${data.Education_Start} - ${data.Education_End}`; }\n            return `<div class=\"timeline-item\"><p class=\"timeline-company\">${school}</p><h3 class=\"timeline-title\">${level}</h3>${dateRange ? `<p class=\"timeline-date\">${dateRange}</p>` : ''}<ul class=\"timeline-tasks\">${this.createListHtml(details)}</ul></div>`;\n        }\n        const fallbackLevel = this.translateEducation(data.EducationLevel);\n        return fallbackLevel ? `<div class=\"timeline-item\"><h3 class=\"timeline-title\">${fallbackLevel}</h3></div>` : '';\n    },\n    createAvatarHtml: () => {\n        // This function now correctly uses the data passed from the previous node.\n        if (data.Avatar !== null) {\n            return `<img src=\"${data.Avatar}\" alt=\"Profilbild\" class=\"profile-pic\">`;\n        } else {\n            return `<div class=\"profile-pic-placeholder\"></div>`;\n        }\n    }\n};\n\n// 3. 完整的HTML模板\nconst finalHtml = `\n<!DOCTYPE html>\n<html lang=\"de\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Lebenslauf - ${data.FirstName} ${data.LastName}</title>\n    <style>\n        html, body {\n            margin: 0; padding: 0; font-family: 'Helvetica Neue', Arial, sans-serif;\n            font-size: 10pt; background-color: #fff; color: #333;\n        }\n        h2 {\n            font-size: 14pt; color: #888; text-transform: uppercase; letter-spacing: 2px;\n            font-weight: 600; margin-top: 0; margin-bottom: 25px; padding-bottom: 5px;\n            border-bottom: 1px solid #ddd;\n            break-after: avoid;\n        }\n        h3 { font-size: 12pt; margin: 0; }\n        p { line-height: 1.6; margin: 5px 0; }\n        \n        .resume-container { display: flex; width: 100%; }\n        \n        /* --- Left Column --- */\n        .left-column {\n            width: 280px;\n            background-color: #f7f7f7;\n            padding: 40px 25px;\n            box-sizing: border-box;\n        }\n        /* MODIFIED: Avatar size reduced */\n        .profile-pic, .profile-pic-placeholder {\n            width: 120px;\n            height: 120px;\n            border-radius: 50%;\n            margin: 0 auto 20px auto;\n            display: block;\n            object-fit: cover;\n            border: 4px solid #fff;\n            box-shadow: 0 0 10px rgba(0,0,0,0.1);\n        }\n        .profile-pic-placeholder { background-color: #e0e0e0; }\n        .name-title { text-align: center; margin-bottom: 20px; }\n        .name-title h1 { font-size: 22pt; margin: 0; word-break: break-word; }\n        .personal-details { text-align: center; font-size: 9pt; color: #555; margin-bottom: 30px; }\n        \n        .section-heading {\n            font-weight: bold;\n            text-transform: uppercase;\n            color: #333;\n            padding-bottom: 5px;\n            border-bottom: 1px solid #ccc;\n            margin-top: 30px;\n            margin-bottom: 15px;\n        }\n        .contact-info p, .skills-info p {\n            margin: 12px 0;\n            font-size: 9.5pt;\n        }\n        .contact-info p { display: flex; align-items: center; }\n        .contact-info .icon { width: 16px; height: 16px; margin-right: 12px; fill: #080071; }\n        .skills-info .skills-list {\n            font-size: 9pt;\n            line-height: 1.5;\n            word-break: break-word;\n        }\n\n        /* --- Right Column --- */\n        .right-column {\n            flex-grow: 1;\n            padding: 40px 35px;\n            box-sizing: border-box;\n        }\n        .timeline { position: relative; padding-left: 25px; border-left: 2px solid #080071; }\n        .timeline-item { margin-bottom: 30px; position: relative; break-inside: avoid; }\n        .timeline-item::before { content: ''; position: absolute; left: -35px; top: 5px; width: 18px; height: 18px; background-color: #080071; border-radius: 50%; border: 4px solid #fff; box-sizing: border-box; }\n        .timeline-company { font-size: 11pt; font-weight: 500; }\n        .timeline-title { color: #080071; font-weight: bold; }\n        .timeline-date { font-size: 9pt; color: #777; margin-bottom: 10px; }\n        .timeline-tasks { list-style-type: disc; padding-left: 20px; margin: 0; font-size: 10pt; }\n        .timeline-tasks li { margin-bottom: 5px; }\n        \n        /* --- Footer --- */\n        footer {\n            height: 24px; background-color: #080071; color: white; padding: 0 16px;\n            display: flex; justify-content: space-between; align-items: center;\n            box-sizing: border-box; z-index: 100;\n        }\n        .footer-left img { height: 16px; vertical-align: middle; }\n        .footer-right { display: flex; align-items: center; }\n        .footer-right a { font-size: 6pt; color: white; text-decoration: none; }\n        .footer-right a:last-child { margin-left: 16px; }\n\n        /* --- Print-specific styles --- */\n        @media print {\n            html, body {\n                height: auto !important;\n                overflow: visible !important;\n                -webkit-print-color-adjust: exact;\n            }\n            body {\n                padding-bottom: 0px; /* Space for the fixed footer */\n            }\n            footer {\n                position: fixed; /* Makes the footer repeat on every page */\n                bottom: 0;\n                left: 0;\n                width: 100%;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"resume-container\">\n        <div class=\"left-column\">\n            <div>\n                ${helpers.createAvatarHtml()}\n                <div class=\"name-title\"><h1>${data.FirstName || ''} ${data.LastName || ''}</h1></div>\n                <div class=\"personal-details\"><p><strong>Nationalität:</strong> ${data.Nationality || 'Nicht angegeben'}</p></div>\n                \n                <div class=\"section-heading\">Kontakt</div>\n                <div class=\"contact-info\">\n                    <p><svg class=\"icon\" viewBox=\"0 0 24 24\"><path d=\"M12 11.5A2.5 2.5 0 0 1 9.5 9A2.5 2.5 0 0 1 12 6.5A2.5 2.5 0 0 1 14.5 9a2.5 2.5 0 0 1-2.5 2.5M12 2a7 7 0 0 0-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 0 0-7-7Z\"></path></svg><span>${data.City || ''}</span></p>\n                    <p><svg class=\"icon\" viewBox=\"0 0 24 24\"><path d=\"M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6m-2 0-8 5-8-5h16m0 12H4V8l8 5 8-5v10Z\"></path></svg><span>${data.Email || ''}</span></p>\n                    <p><svg class=\"icon\" viewBox=\"0 0 24 24\"><path d=\"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2Z\"></path></svg><span>${data.PhoneNumber || ''}</span></p>\n                </div>\n\n                <br></br><br></br>\n                <div class=\"skills-info\">\n                    <p><strong>Sprachen:</strong> Deutsch (${data.GermanLevel ? data.GermanLevel.toUpperCase() : ''}), ${data.OtherLanguages || ''}</p>\n                    <p><strong>Führerschein:</strong> ${helpers.translateDrivingLicense(data.DrivingLicense)}</p>\n                    <p><strong>Fähigkeiten & Zertifikate:</strong></p>\n                    <p class=\"skills-list\">${data.SkillsAndCerts || ''}</p>\n                </div>\n\n            </div>\n        </div>\n        <div class=\"right-column\">\n            <h2>Berufserfahrung</h2>\n            <div class=\"timeline\">${helpers.createWorkExperienceHtml()}</div>\n            <h2>Bildungsweg</h2>\n            <div class=\"timeline\">${helpers.createEducationHtml()}</div>\n        </div>\n    </div>\n    <footer>\n        <div class=\"footer-left\"><img src=\"https://storage.googleapis.com/sohnus-logo/Logo-footer-slogan.png\" alt=\"Sohnus Logo\"></div>\n        <div class=\"footer-right\"><a href=\"https://www.sohnus.de\">www.sohnus.de</a><a href=\"mailto:<EMAIL>\"><EMAIL></a></div>\n    </footer>\n</body>\n</html>\n`;\n\n// 4. 返回最终的HTML内容\nreturn { finalHtml: finalHtml };\n"}, "id": "1037a87f-9cd0-4d98-9a16-0c80a655192f", "name": "Code: 生成完整HTML", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-240, 200]}, {"parameters": {"operation": "toText", "sourceProperty": "finalHtml", "options": {"fileName": "index.html"}}, "id": "aeb000b5-d097-4526-86bc-17825b5bc6f5", "name": "Convert to File", "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [20, 200]}, {"parameters": {"method": "POST", "url": "https://gotenberg-544103549891.europe-west3.run.app/forms/chromium/convert/html", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "files", "inputDataFieldName": "data"}, {"name": "marginTop", "value": "0"}, {"name": "marginBottom", "value": "0"}, {"name": "marginLeft", "value": "0"}, {"name": "marginRight", "value": "0"}]}, "options": {"response": {"response": {"responseFormat": "file"}}}}, "id": "********-f768-4ae8-9cd8-76dc4ddacc00", "name": "调用Gotenberg生成PDF", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [280, 200], "alwaysOutputData": false}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-1760, 40], "id": "422ce69f-c2b6-49f6-9828-db94d6ba0516", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "KoLMC5gNVERMJw2H", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-1900, 40], "id": "ec8c08f6-ff30-4272-aa30-d2fd0588f6dc", "name": "Google Gemini Chat Model1", "credentials": {"googlePalmApi": {"id": "KoLMC5gNVERMJw2H", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"promptType": "define", "text": "={{ JSON.stringify($json.dataToTranslate) }}", "messages": {"messageValues": [{"message": "You are a precise data processing API. Your sole function is to receive a JSON object, translate the values of its properties into German, and return ONLY the modified JSON object.\n\nFollow these rules strictly:\n\n1.  Your entire output must be a single, valid JSON object.\n\n2.  DO NOT wrap the output in Markdown code blocks like ```json ... ```.\n\n3.  DO NOT add any explanatory text, greetings, or apologies before or after the JSON.\n4.  Keep the original JSON keys and structure.\n5.  Do not translate emails, URLs, dates, numbers, or technical codes. Do NOT translate the keys of the JSON, only the values.\n6.  Translate all other descriptive text fields into German.\n7.  For the fields `Job1_Tasks`, `Job2_Tasks`, `Job3_Tasks`, and `Education_Detail`, rephrase the content into a concise, professional, bulleted list. Each bullet point MUST start with a newline character (`\\n`) followed by a hyphen and a space (`- `).\n    * Example Input: \"Responsible for developing applications, working on system architecture, and collaborating with teams.\"\n    * Example Output: \"\\n- Entwicklung von Anwendungen\\n- Mitwirkung an der Systemarchitektur\\n- Zusammenarbeit mit Teams\"\n8.  For fields `Job1_Company`, `Job2_Company`, `Job3_Company`, `Education_School`:\n    * If the name is Chinese, translate it into German as well.\n9.  Translate Chinese names into Pinyin. \n10. For Nationality, if it's country's name, translate into nationality.\n\nProcess the input JSON according to these rules and return only the modified JSON object."}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-920, 200], "id": "0b834b83-f253-4b5c-ab18-b8392efda99f", "name": "CV Content Translator", "alwaysOutputData": false}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-880, 420], "id": "990622ea-9867-40ae-94df-025c144a19ec", "name": "Google Gemini Chat Model2", "credentials": {"googlePalmApi": {"id": "KoLMC5gNVERMJw2H", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "db04dfa0-acb8-4d84-880e-ddc756202340", "name": "dataToTranslate", "value": "={{ $json }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1220, 200], "id": "e776c3b4-efa3-4b88-81ea-a4c5f7ef104e", "name": "提取待翻译数据"}, {"parameters": {"jsCode": "// 上一个AI节点的输出是一个像 { \"text\": \"{\\\"Id\\\":1, ...}\" } 这样的对象。\n// 我们首先需要获取 \"text\" 属性的值，它是一个包含了JSON内容的字符串。\nconst jsonString = $input.first().json.text;\n\n// 使用JavaScript内置的 JSON.parse() 函数，\n// 将这个字符串转换回一个真正的、可操作的JSON对象。\nconst parsedData = JSON.parse(jsonString);\n\n// 返回这个解析后的JSON对象，给工作流中的下一个节点使用。\nreturn parsedData;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-500, 200], "id": "f8cad3b7-20a0-4ebc-baea-47270cb31b83", "name": "解包"}, {"parameters": {"content": "## Notice:\nThis Translator will be replaced by a NLP dictionary", "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-920, -40], "id": "683dcdf4-6dd2-4f56-8ddc-04c6c4c7807a", "name": "<PERSON><PERSON>"}, {"parameters": {"authentication": "nocoDbApiToken", "projectId": "ptv4ombrshshagj", "table": "mcv0hz2nurqap37", "id": "={{ $json.body.data.rows[0].Id }} "}, "type": "n8n-nodes-base.nocoDb", "typeVersion": 3, "position": [-3240, -240], "id": "14882e0e-5d79-47c7-8e95-11c9f475fd02", "name": "Get a row", "credentials": {"nocoDbApiToken": {"id": "dyeHbxUQYmSH5Zqc", "name": "NocoDB Token account"}}}, {"parameters": {"subject": "={{ $('提取待翻译数据').item.json.dataToTranslate.parseJson().FirstName }} ({{ $('提取待翻译数据').item.json.dataToTranslate.parseJson().Language }}) CV  ", "textContent": "=<p>A new CV has been generated for a candidate.</p>\n<p><strong>Name:</strong> {{ $('提取待翻译数据').item.json.dataToTranslate.parseJson()[\"FirstName\"] }} {{ $('提取待翻译数据').item.json.dataToTranslate.parseJson()[\"LastName\"] }}</p>\n<p><strong>Language:</strong> {{ $('提取待翻译数据').item.json.dataToTranslate.parseJson()[\"Language\"] }}</p>\n<p><strong>Email:</strong> {{ $('提取待翻译数据').item.json.dataToTranslate.parseJson()[\"Email\"] }}</p>\n<p>The CV is attached to this email.</p>", "sender": "<EMAIL>", "receipients": "<EMAIL>", "additionalFields": {"emailAttachments": {"attachment": {"binaryPropertyName": "=data"}}}, "requestOptions": {}}, "type": "n8n-nodes-base.sendInBlue", "typeVersion": 1, "position": [560, 60], "id": "a4bf9c41-273f-4e8a-b737-7b44d4d67887", "name": "Send a transactional email", "credentials": {"sendInBlueApi": {"id": "Kmvo6nnL4RPWd4lW", "name": "Brevo account"}}}, {"parameters": {"amount": 10}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-3460, -240], "id": "44d3ff80-4947-4385-83d7-0818a790ab0e", "name": "Wait", "webhookId": "03647a33-5ad7-4d6a-92cb-68dc0163ab46"}, {"parameters": {"authentication": "nocoDbApiToken", "operation": "update", "projectId": "ptv4ombrshshagj", "table": "mcv0hz2nurqap37", "fieldsUi": {"fieldValues": [{"fieldName": "Id", "fieldValue": "={{ $('解包').item.json.Id }}"}, {"fieldName": "Status", "fieldValue": "CVGenerated"}, {"fieldName": "GeneratedCV", "fieldValue": "={{ $json.fileUrl }}"}]}}, "type": "n8n-nodes-base.nocoDb", "typeVersion": 3, "position": [980, 280], "id": "554cf5e6-b5dc-4e8f-af44-263a38302600", "name": "Update a row", "credentials": {"nocoDbApiToken": {"id": "dyeHbxUQYmSH5Zqc", "name": "NocoDB Token account"}}}, {"parameters": {"method": "POST", "url": "https://storage.googleapis.com/upload/storage/v1/b/worker-cvs/o", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleApi", "sendQuery": true, "queryParameters": {"parameters": [{"name": "uploadType", "value": "media"}, {"name": "name", "value": "=cv_{{$('提取待翻译数据').item.json.dataToTranslate.parseJson()[\"FirstName\"]}}_{{$('提取待翻译数据').item.json.dataToTranslate.parseJson()[\"LastName\"]}}_{{$timestamp}}.pdf"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/pdf"}]}, "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [560, 280], "id": "0c377731-cdc3-4ee1-a4aa-61cdfa8bcccf", "name": "上传CV到GCS", "credentials": {"googleApi": {"id": "VrXQZtoTIaLzQYSp", "name": "Google Service Account account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "2c25c831-4c9b-4866-9403-aefbfbfefa3a", "name": "fileUrl", "value": "=https://storage.googleapis.com/{{$json.bucket}}/{{$json.name}}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [780, 280], "id": "51af254a-c856-47a2-9fe0-3eef7d4c0cde", "name": "获取CV链接"}, {"parameters": {"method": "POST", "url": "https://open.larksuite.com/open-apis/bot/v2/hook/a466cdd6-3a5d-4fd1-aea8-c2997f6cf161", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n\t\"msg_type\": \"text\",\n\t\"content\": {\n\t\t\"text\": \"Form submission Notice \\n Worker “ {{ $json.FirstName }} {{ $json.LastName }}” has just submitted a form in {{ $json.Language }}。Current status: {{ $json.Status }}\"\n\t}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1460, -280], "id": "e8be59c8-8203-487b-89d1-fe29f48a9a17", "name": "Send message to Lark"}, {"parameters": {"authentication": "nocoDbApiToken", "projectId": "ptv4ombrshshagj", "table": "mcv0hz2nurqap37", "id": "={{ $json.Id }} "}, "type": "n8n-nodes-base.nocoDb", "typeVersion": 3, "position": [1180, 280], "id": "a233939d-42fc-4023-b0ff-d0f6cf3aa12f", "name": "Get updated data", "credentials": {"nocoDbApiToken": {"id": "dyeHbxUQYmSH5Zqc", "name": "NocoDB Token account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "80caf23d-e9f7-42fa-89f7-7abb85b73bbd", "leftValue": "={{ $json.Status }}", "rightValue": "Connect", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-2960, -240], "id": "d8346ac5-bb8c-41c7-8df3-e054a5f0f076", "name": "IF: Only chat?"}, {"parameters": {"content": "## Notice:\nThis node will be replaced by a NLP dictionary", "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1860, -400], "id": "2219b418-02cd-4155-ab3d-92ec60b8cf33", "name": "Sticky Note1"}], "pinData": {}, "connections": {"NocoDB Trigger": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "IF: PDF exists?": {"main": [[{"node": "Download PDF", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Download PDF": {"main": [[{"node": "Extract Text from PDF", "type": "main", "index": 0}]]}, "Extract Text from PDF": {"main": [[{"node": "AI解析简历文本", "type": "main", "index": 0}]]}, "AI解析简历文本": {"main": [[{"node": "Set: 合并数据", "type": "main", "index": 0}]]}, "NoOp": {"main": [[{"node": "提取待翻译数据", "type": "main", "index": 0}]]}, "Set: 合并数据": {"main": [[{"node": "提取待翻译数据", "type": "main", "index": 0}]]}, "Code: 生成完整HTML": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "调用Gotenberg生成PDF", "type": "main", "index": 0}]]}, "调用Gotenberg生成PDF": {"main": [[{"node": "Send a transactional email", "type": "main", "index": 0}, {"node": "上传CV到GCS", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI解析简历文本", "type": "ai_languageModel", "index": 1}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "AI解析简历文本", "type": "ai_languageModel", "index": 0}]]}, "CV Content Translator": {"main": [[{"node": "解包", "type": "main", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "CV Content Translator", "type": "ai_languageModel", "index": 0}]]}, "提取待翻译数据": {"main": [[{"node": "CV Content Translator", "type": "main", "index": 0}]]}, "解包": {"main": [[{"node": "Code: 生成完整HTML", "type": "main", "index": 0}]]}, "Get a row": {"main": [[{"node": "IF: Only chat?", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Get a row", "type": "main", "index": 0}]]}, "Send a transactional email": {"main": [[]]}, "上传CV到GCS": {"main": [[{"node": "获取CV链接", "type": "main", "index": 0}]]}, "获取CV链接": {"main": [[{"node": "Update a row", "type": "main", "index": 0}]]}, "Update a row": {"main": [[{"node": "Get updated data", "type": "main", "index": 0}]]}, "Get updated data": {"main": [[{"node": "Send message to Lark", "type": "main", "index": 0}]]}, "IF: Only chat?": {"main": [[{"node": "Send message to Lark", "type": "main", "index": 0}], [{"node": "IF: PDF exists?", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "fdffa408-c1eb-4ddb-a488-b476a021e6af", "meta": {"templateCredsSetupCompleted": true, "instanceId": "36da3f6eda1d57115c10d4e4bd55c0be5401554fa8cab2d07c58a7aea769a71f"}, "id": "Y4QFm38TjeDRlQC8", "tags": []}